import{D as N,G as C,u as R,r as g,a5 as V,E as n,o as O,a6 as J,a3 as b,g as u,t as L,v as j,x as e,y as l,z as i,A as r,B as T,a7 as q,aa as P}from"./BHVVUzUw.js";import{_ as z}from"./DlAUqK2U.js";const F={class:"bg-background text-foreground antialiased min-h-screen flex items-center justify-center"},G={class:"cl-rootBox cl-signUp-root justify-center"},M={class:"cl-cardBox cl-signUp-start"},E={class:"cl-card cl-signUp-start"},W={class:"cl-header"},Z={class:"cl-headerTitle"},D={class:"cl-headerSubtitle"},H={class:"cl-main"},Q={class:"cl-socialButtonsRoot"},K={class:"cl-socialButtons"},X={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},Y={class:"cl-socialButtonsBlockButton-d"},oo={class:"cl-socialButtons"},eo={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},to={class:"cl-socialButtonsBlockButton-d"},so={class:"cl-dividerRow"},no={class:"cl-dividerText"},lo={class:"cl-socialButtonsRoot"},io={class:"cl-internal-1pnppin"},ao={class:"cl-internal-742eeh"},co={class:"cl-internal-2iusy0"},ro={class:"cl-footer cl-internal-4x6jej"},uo={class:"cl-footerAction cl-footerAction__signUp cl-internal-1rpdi70"},po={class:"cl-footerActionText cl-internal-kyvqj0","data-localization-key":"signUp.start.actionText"},mo={__name:"index",setup(_o){var w;const{t:d,locale:_}=N(),f=C();R();const I=f.params.socialType,v=f.query.authCode,k=f.query.authState,h=g(),a=g({email:"",password:""});location!=null&&location.origin.includes("medon.com.cn")||(location==null||location.origin.includes("medsci.cn"));const B=g(),S=V({password:[{required:!0,message:d("tool.password_cannot_be_empty"),trigger:"blur"},{min:8,max:20,message:d("tool.pwdLength"),trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[a-zA-Z\d\W_]{8,20}$/,message:d("tool.password_includes_uppercase_lowercase_numbers_and_symbols"),trigger:"blur"}],email:[{required:!0,message:d("tool.email_address_cannot_be_empty"),trigger:"blur"},{type:"email",message:d("tool.please_enter_a_valid_email_address"),trigger:"blur"}]}),x=n.get("userInfo")?(w=JSON.parse(n.get("userInfo")))==null?void 0:w.userId:"",y=o=>{q(o).then(t=>{window.location.href=t})},U=async o=>{o&&await o.validate((t,p)=>{t&&P(a.value).then(s=>{if(s!=null&&s.token&&(s!=null&&s.htoken)){b.set("yudaoToken",s.token),localStorage.setItem("hasuraToken",s.htoken),localStorage.setItem("openid",s.openid),localStorage.setItem("socialUserId",s.socialUserId),localStorage.setItem("socialType",s.socialType),s.userInfo.userId=s.userInfo.openid,s.userInfo.plaintextUserId=s.userInfo.socialUserId,s.userInfo.token={accessToken:s.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:s.userInfo.openid},location.origin.includes(".medsci.cn")?n.set("userInfo",JSON.stringify(s.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?n.set("userInfo",JSON.stringify(s.userInfo),{expires:365,domain:".medon.com.cn"}):n.set("userInfo",JSON.stringify(s.userInfo),{expires:365});const m=window.sessionStorage.getItem("redirectUrl");m?(window.location.href=m,window.sessionStorage.removeItem("redirectUrl")):window.location.href="/"}else console.error("登录失败: 未返回 token")})})};return O(()=>{h.value=(location==null?void 0:location.origin)+"/"+_.value+"/sign-up",x?location.href=location.origin+"/"+_.value+"/":I&&v&&k&&J(I,v,k).then(o=>{o!=null&&o.token&&(o!=null&&o.htoken)?(b.set("yudaoToken",o.token),localStorage.setItem("hasuraToken",o.htoken),localStorage.setItem("openid",o.openid),localStorage.setItem("socialUserId",o.socialUserId),localStorage.setItem("socialType",o.socialType),o.userInfo.userId=o.userInfo.openid,o.userInfo.plaintextUserId=o.userInfo.socialUserId,o.userInfo.token={accessToken:o.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:o.userInfo.openid},location.origin.includes(".medsci.cn")?n.set("userInfo",JSON.stringify(o.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?n.set("userInfo",JSON.stringify(o.userInfo),{expires:365,domain:".medon.com.cn"}):n.set("userInfo",JSON.stringify(o.userInfo),{expires:365}),location.href=location.origin+"/"+_.value+"/"):console.error("登录失败: 未返回 token")})}),(o,t)=>{const p=u("el-input"),s=u("el-form-item"),m=u("el-button"),$=u("el-form"),A=u("el-link");return j(),L("div",F,[e("div",G,[e("div",M,[e("div",E,[e("div",W,[e("div",null,[e("h1",Z,l(o.$t("tool.login_to_MedSci_xAI")),1),e("p",D,l(o.$t("tool.welcome_back_please_login_to_continue")),1)])]),e("div",H,[e("div",Q,[e("div",K,[e("button",X,[e("span",Y,[t[5]||(t[5]=e("span",null,[e("img",{src:"https://img.medsci.cn/202412/cc7c7687b4804460a85d46c629132724-vcHTUHMGIylQ.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1)),e("span",{class:"cl-socialButtonsBlockButtonText",onClick:t[0]||(t[0]=c=>y(35))},l(o.$t("tool.continue_with_google")),1)])])]),e("div",oo,[e("button",eo,[e("span",to,[t[6]||(t[6]=e("span",null,[e("img",{src:"https://img.medsci.cn/202412/f9bf95f463c04d3e801aa6e97ef3d4b8-OSsmrMWR677i.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1)),e("span",{class:"cl-socialButtonsBlockButtonText",onClick:t[1]||(t[1]=c=>y(36))},l(o.$t("tool.continue_with_facebook")),1)])])])]),e("div",so,[t[7]||(t[7]=e("div",{class:"cl-dividerLine"},null,-1)),e("p",no,l(o.$t("tool.or")),1),t[8]||(t[8]=e("div",{class:"cl-dividerLine"},null,-1))]),e("div",lo,[i($,{ref_key:"ruleFormRef",ref:B,style:{"max-width":"600px"},model:a.value,rules:S,"label-width":"auto",class:"demo-ruleForm","label-position":"left","status-icon":""},{default:r(()=>[i(s,{label:o.$t("tool.email"),prop:"email"},{default:r(()=>[i(p,{modelValue:a.value.email,"onUpdate:modelValue":t[2]||(t[2]=c=>a.value.email=c)},null,8,["modelValue"])]),_:1},8,["label"]),i(s,{label:o.$t("tool.password"),prop:"password",style:{"padding-bottom":"20px"}},{default:r(()=>[i(p,{modelValue:a.value.password,"onUpdate:modelValue":t[3]||(t[3]=c=>a.value.password=c),"show-password":!0},null,8,["modelValue"])]),_:1},8,["label"]),i(s,null,{default:r(()=>[e("div",io,[t[10]||(t[10]=e("div",{id:"clerk-captcha",class:"cl-internal-3s7k9k"},null,-1)),e("div",ao,[i(m,{class:"cl-formButtonPrimary cl-button cl-internal-ttumny",onClick:t[4]||(t[4]=c=>U(B.value))},{default:r(()=>[e("span",co,[T(l(o.$t("tool.continue")),1),t[9]||(t[9]=e("svg",{class:"cl-buttonArrowIcon cl-internal-1c4ikgf"},[e("path",{fill:"currentColor",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"m7.25 5-3.5-2.25v4.5L7.25 5Z"})],-1))])]),_:1})])])]),_:1})]),_:1},8,["model","rules"])])])]),e("div",ro,[e("div",uo,[e("span",po,l(o.$t("tool.no_account_yet")),1),i(A,{href:h.value,class:"cl-footerActionLink"},{default:r(()=>[T(l(o.$t("tool.signUp")),1)]),_:1},8,["href"])])])])])])}}},Io=z(mo,[["__scopeId","data-v-605ca68e"]]);export{Io as default};
