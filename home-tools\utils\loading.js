import { ElLoading } from "element-plus";
import {getItemWithTimestampCheck} from "@/common/commonJs"
import storage from "@/utils/storage";

const lang = storage.get('ai_apps_lang') || 'en'
const langes = getItemWithTimestampCheck('current_langs_pack',7) ? JSON.parse(getItemWithTimestampCheck('current_langs_pack',7))[lang]?.tool : '数据加载中'
const defaultOption = {
  lock: true,
  text: langes['loadingData'],
  background: "rgba(225, 225, 225, 0.3)",
};
// loading实例
let loading;

export const $loading = {
  // 打开loading
  showLoading: (opt) => {
    loading = ElLoading.service(opt || defaultOption);
  },
  // 关闭loading
  hideLoading: () => {
    if (loading) {
      loading.close();
      loading = null;
    }
  },
};
