import { PlusOutlined, UserOutlined } from '@ant-design/icons'
import { XProvider } from '@ant-design/x'
import {Cookies} from '../utils/cookieHandler'
import {
	createDifyApiInstance,
	DifyApi,
	IGetAppInfoResponse,
	IGetAppParametersResponse,
} from '@dify-chat/api'
import { ConversationList, type IConversationItem } from '@dify-chat/components'
import { IDifyAppItem, IDifyChatContextMultiApp } from '@dify-chat/core'
import { useDifyChat } from '@dify-chat/core'
import { Button, Divider, Empty, message, Space, Spin, Popover,Avatar } from 'antd'
import { createStyles } from 'antd-style'
import { useSearchParams } from 'pure-react-router'
import React, { useEffect, useMemo, useState } from 'react'
import { useHistory, useParams } from 'pure-react-router'
import { useTranslation } from 'react-i18next'
import ChatboxWrapper from '@/components/chatbox-wrapper'
import LanguageSelector from '@/components/language-selector'
import { GithubIcon, Logo } from '@/components/logo'

import { useMap4Arr } from '@/hooks/use-map-4-arr'
import { colors } from '@/theme/config'
import AppService from '../services/app/restful'
import AppServices from '../services/app/request'
import AssistantComponent from './components/AssistantComponent';
import './../App.css'
import CenterTitleWrapper from './components/center-title-wrapper'

const useStyle = createStyles(({ token, css }) => {
	return {
		layout: css`
			font-family: AlibabaPuHuiTi, ${token.fontFamily}, sans-serif;
		`,
		menu: css`
			background: ${token.colorBgLayout}80;
		`,
	}
})

interface IBaseLayoutProps {
	/**
	 * 扩展的 JSX 元素, 如抽屉/弹窗等
	 */
	extComponents?: React.ReactNode
	/**
	 * 自定义中心标题
	 */
	renderCenterTitle?: (appInfo: IDifyAppItem['info']) => React.ReactNode
	/**
	 * 自定义右侧头部内容
	 */
	renderRightHeader?: () => React.ReactNode
	/**
	 * 获取当前应用配置
	 */
	appConfig: IDifyAppItem
	/**
	 * 初始化应用信息
	 */
	useAppInit: (difyApi: DifyApi, callback: () => void) => void
	/**
	 * 触发配置应用事件
	 */
	handleStartConfig: () => void
	/**
	 * 是否正在加载应用配置
	 */
	initLoading: boolean

	selectedAppId:string,
	appList:any
}

const BaseLayout = (props: IBaseLayoutProps) => {
	const [modal2Open, setModal2Open] = useState(false)
	const history = useHistory()
const appService = new AppService()
	const {
		extComponents,
		appConfig,
		useAppInit,
		renderCenterTitle,
		handleStartConfig,
		initLoading,
		selectedAppId,
		appList
	} = props
	const { ...difyChatContext } = useDifyChat()
	const { user } = difyChatContext as IDifyChatContextMultiApp
	const { t } = useTranslation()
	// 创建 Dify API 实例
	const { styles } = useStyle()
	const [difyApi] = useState(
		createDifyApiInstance({
			user,
			apiBase: '',
			apiKey: '',
			appId:''
		}),
	)
	const searchParams = new URLSearchParams((window as any).top.location.search)
	const [conversationsItems, setConversationsItems] = useState<IConversationItem[]>([])
	// 优化会话列表查找逻辑（高频操作）
	const conversationMap = useMap4Arr<IConversationItem>(conversationsItems, 'key')
	const [conversationListLoading, setCoversationListLoading] = useState<boolean>(false)
	const [currentConversationId, setCurrentConversationId] = useState<string>()
	const [appInfo, setAppInfo] = useState<IGetAppInfoResponse>()
	const [appParameters, setAppParameters] = useState<IGetAppParametersResponse>()
	const [appConfigLoading, setAppConfigLoading] = useState(false)
	const [inputParams, setInputParams] = useState<{ [key: string]: unknown }>({})
	const [subStatusDetail, setSubStatusDetail]:any = useState({})
	const [hasInitializedNewSession, setHasInitializedNewSession] = useState(false)
	const match = history.location.pathname.match(/^\/ai-chat\/([^/]+)$/)
	const appNameEn = match ? match[1] : ''
	const [onSubmit, setOnSubmit] = useState(null)
	/**
	 * 重置输入表单值
	 */
	const resetFormValues = () => {
		// 遍历 inputParams 置为 undefined
		if (appParameters?.user_input_form?.length) {
			const newInputParams = { ...inputParams }
			appParameters?.user_input_form.forEach(item => {
				const field = item['text-input']
				newInputParams[field.variable] = undefined
			})
			setInputParams(newInputParams)
		}
	}
	
	const initAppInfo = async () => {
		setAppInfo(undefined)
		if (!difyApi) {
			return
		}
		setAppConfigLoading(true)
		// 获取应用信息
		// const baseInfo = await difyApi.getAppInfo()
		// setAppInfo({
		// 	...baseInfo,
		// })
		setAppInfo({
			name: appConfig.info.name,
			description: appConfig.info.description,
			tags: []
		})
		const appParameters = await difyApi.getAppParameters()
		setAppParameters(appParameters)
		setAppConfigLoading(false)
	}

	useAppInit(difyApi, () => {
		// 重置新会话初始化标志
		setHasInitializedNewSession(false)
		initAppInfo().then(() => {
			getConversationItems()
		})
		resetFormValues()
		setCurrentConversationId(undefined)
	})

	/**
	 * 获取对话列表
	 */
	const getConversationItems = async () => {
		setCoversationListLoading(true)
		try {
			const newKey = `temp_${Math.random()}`
			let newItems: IConversationItem[] = []
			   if(userInfoString){
				// 已订阅
				const result = await difyApi?.getConversationList()
				if(result?.code == 401){
					Cookies.remove("userInfo", { domain: ".medon.com.cn" });
					Cookies.remove("userInfo", { domain: ".medsci.cn" });
					Cookies.remove("userInfo", { domain: "localhost" });
					Cookies.remove("yudaoToken", { domain: "ai.medon.com.cn" });
					Cookies.remove("yudaoToken", { domain: "ai.medsci.cn" });
					Cookies.remove("yudaoToken", { domain: ".medon.com.cn" });
					Cookies.remove("yudaoToken", { domain: ".medsci.cn" });
					Cookies.remove("yudaoToken", { domain: "localhost" });
					localStorage.removeItem("conversation")
					if ((window as any).location.origin.includes("medsci.cn")) {
					(window as any).top.location.href =
						"https://www.medsci.cn/sso_logout?redirectUrl=" + (window as any).top.location.href;
					} else {
					(window as any).top.location.href =
						`https://portal-test.medon.com.cn/sso_logout?redirectUrl=` +
						(window as any).top.location.href;
					}
				}
				if(result?.data?.length) {
					newItems =
						result?.data?.map(item => {
							return {
								key: item.id,
								label: item.name,
							}
						})

					// 只有在有历史记录且没有新会话时才创建新会话
					const hasNewSession = newItems.some(item => item.key.startsWith('temp_'))
					if (!hasNewSession && !hasInitializedNewSession) {
						setHasInitializedNewSession(true)
						// 延迟执行，确保状态已更新
						setTimeout(() => {
							onAddConversation()
						}, 0)
					}
				}else {
					// 没有历史记录时，不创建任何会话项
					newItems = []
				}
			   }

				// newItems =
				// 	result?.data?.map(item => {
				// 		return {
				// 			key: item.id,
				// 			label: item.name,
				// 		}
				// 	})
			setConversationsItems(newItems)

			// if(!((appNameEn == 'medsci-ask')||(searchParams.get('fromPlatform')))){
			// 	setCurrentConversationId(newItems[0]?.key)
			// }
		} catch (error) {
			console.error(error)
			// message.error(`获取会话列表失败: ${error}`)
		} finally {
			setCoversationListLoading(false)
		}
	}
	const language = () => {
		return Cookies.get(
			'ai_apps_lang'
		)? Cookies.get('ai_apps_lang')
		: navigator.browserLanguage || navigator.language
	}
	/**
	 * 添加临时新对话(要到第一次服务器响应有效的对话 ID 时才真正地创建完成)
	 */
	const onAddConversation =async () => {
		const appServices = new  AppServices()
		const match = history.location.pathname.match(/^\/ai-chat\/([^/]+)$/)
		const appNameEn = match ? match[1] : ''
		
		let languages	= language()
		if (user == 'nologin') {
			// 弹出登录框
			// message.error('未登录, 弹出登录框')
			// 
			 // 判断是否是国内用户访问
			 if (!languages || languages == "zh") {
				window.addLoginDom();
			  } else {
				// 跳转到获取授权页
				window.top.location.href =  location.origin + '/' + languages + "/login"
				// router.push(isUp ? "/login" : "/login");
			  }
			return
		}


		// 创建新对话
		const newKey = `temp_${Math.random()}`
		// 使用函数式更新保证状态一致性（修复潜在竞态条件）
		setConversationsItems(prev => {
			const newItems = [
				{
					key: newKey,
					label: t('chat.newSession'),
				},
				...prev,
			]
			// 在状态更新的同时设置当前会话ID，确保同步
			setTimeout(() => {
				setCurrentConversationId(newKey)
			}, 0)
			return newItems
		})
		if(appNameEn.includes("elavax-pro")||appNameEn.includes("novax-pro")){
			const res =await appServices.bindAppUser({
				// 使用之前获取的 match 变量
				appUuid: appConfig?.id||'',
				appNameEn
			},{Authorization: `Bearer ${Cookies.get("yudaoToken")}`,
			Accept: 'application/json',})
			if(res?.data.remainNum==0){
			}else{
				setCurrentConversationId(newKey)
				return false
			}
		}
		const appUuid = match ? match[1] : ''
		let appInfoNew:any = await appService.getApp( appUuid,{
			   Authorization: `Bearer ${Cookies.get("yudaoToken")}`,
			   Accept: 'application/json'
		   })
		   if (!appInfoNew.info.appUser || appInfoNew?.info.appUser?.status == 2) {
			   // 没有订阅
			   if(document.getElementsByTagName('textarea')?.length){
				document.getElementsByTagName('textarea')[0].blur()

			   }
			   // 没有订阅
			   setModal2Open(true)
			   // message.error('未订阅, 弹出订阅框')
			   return
		   }
		   setCurrentConversationId(newKey)
	}
	useEffect(() => {
		initSub()
	}, [])
	useEffect(() => {
		// 如果对话 ID 不在当前列表中，则刷新一下
		if (currentConversationId && !conversationMap.has(currentConversationId)) {
			getConversationItems()
		}
	}, [currentConversationId])

	const conversationName = useMemo(() => {
		return (
			conversationsItems.find(item => item.key === currentConversationId)?.label ||
			t('chat.newSession')
		)
	}, [conversationsItems, currentConversationId])
	useEffect(() => {
		if (!appConfig) {
			setConversationsItems([])
			setAppInfo(undefined)
			setCurrentConversationId('')
			resetFormValues()
		}
	}, [appConfig])

	const userInfoString = Cookies.get('userInfo')
	const avatar = userInfoString&&JSON.parse(userInfoString).avatar?JSON.parse(userInfoString).avatar:'https://img.medsci.cn/web/img/user_icon.png'
	const errorImg = (e:any) => {
		e.target.src = 'https://img.medsci.cn/web/img/user_icon.png';
	}
	const initSub=async ()=>{
		const appservice = new  AppServices()
	 	const res:any = await appservice.getPackageByKey({},{
			Authorization: `Bearer ${Cookies.get("yudaoToken")}`,
			Accept: 'application/json'
		})
		setSubStatusDetail(res.data)
	}
	// 获取订阅信息


	const [arrow, setArrow] = useState(false)
	const login = () => {
		const languages =  language();
		if (!languages ||  languages === 'zh') {
		  // 为了解决 TypeScript 类型错误，需要先声明 window.addLoginDom 方法
		  (window as any).addLoginDom?.()
		} else {
		  window.top.location.href =  location.origin + '/' + languages + "/login"
		}
		// Replace with actual login logic
	  };
	  const logout =async () => {
		setArrow(false)
		Cookies.remove("userInfo", { domain: ".medon.com.cn" });
		Cookies.remove("userInfo", { domain: ".medsci.cn" });
		Cookies.remove("userInfo", { domain: "localhost" });
		localStorage.removeItem("hasuraToken");
		Cookies.remove("yudaoToken", { domain: "ai.medon.com.cn" });
		Cookies.remove("yudaoToken", { domain: "ai.medsci.cn" });
		Cookies.remove("yudaoToken", { domain: ".medon.com.cn" });
		Cookies.remove("yudaoToken", { domain: ".medsci.cn" });
		Cookies.remove("yudaoToken", { domain: "localhost" });
		localStorage.removeItem("conversation")
		if (window.location.origin.includes("medsci.cn")) {
		  window.top.location.href =
			"https://www.medsci.cn/sso_logout?redirectUrl=" + window.top.location.href;
		} else {
		  window.top.location.href =
			`https://portal-test.medon.com.cn/sso_logout?redirectUrl=` +
			window.top.location.href;
		}
	  };
	return (
		<XProvider theme={{ token: { colorPrimary: colors.primary, colorText: colors.default } }}>
			<div
				className={`w-full h-screen ${styles.layout} flex flex-col overflow-hidden bg-[#eff0f5]`}
			>
				<AssistantComponent />
				{/* 头部 */}
				<div className="hidden md:!flex items-center justify-between px-6">
					{/* 🌟 Logo */}
					<div className={`flex-1 overflow-hidden ${appConfig ? '' : 'shadow-sm'}`}>
						<Logo hideGithubIcon={true} />
					</div>

					<CenterTitleWrapper>
						{(appNameEn.includes("novax")||appNameEn.includes("elavax"))
              ? (renderCenterTitle && appInfo 
                  ? renderCenterTitle({
                      name: appInfo.name,
                      description: appInfo.description,
                      tags: appInfo.tags || [],
                      appUser: null,
                      appId: '',
                      appType: '',
                      appIcon: '',
                      feeTypes: []
                    }) 
                  : null) 
              : (appConfig ? appConfig.info.name : '')}
					</CenterTitleWrapper>

					{/* 右侧图标 */}
					<div className="flex-1 overflow-hidden">
						<div className="flex items-center justify-end text-sm">
						{language() == 'zh' &&(!appNameEn.includes("novax")&&!appNameEn.includes("elavax"))&&<div className='cursor-pointer px-[15px] py-[4px] flex items-center h-[28px] rounded border-none text-xs mr-[8px] text-[#614018]' style={{  backgroundImage: "linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)"}} onClick={()=>{initSub();setModal2Open(true)}}>{  subStatusDetail.packageType == "免费"?t('subscription.upgrade'):subStatusDetail.packageType == "连续包月"||subStatusDetail.packageType == "连续包年"?t('subscription.modify'):t('subscription.subscribe') }</div>}
							<a  style={{borderRadius:'4px',background:'#f1f5f9',padding:'6px 10px',fontSize:'12px',color:"#666",marginRight:"23px"}} className='backImg'   href={location.origin.includes(".medon.com.cn")?('https://ai.medon.com.cn'+'/'+language()):location.origin.includes(".medsci.cn")?('https://ai.medsci.cn'+'/'+language()):'/'+language()} target='_top'>{t('common.backToHome')}</a>
							{!userInfoString&&<div className="hover:cursor-pointer" onClick={login}>{t('common.login')}</div>}
							<Popover
								placement="bottomLeft"
								trigger="hover"
								arrow={arrow}
								overlayStyle={{
									width: 350,
									paddingBottom:40,
								}}
								content={
									<div style={{
									display: 'flex',
									flexDirection: 'column',
									alignItems: 'center',
									position: 'relative',
									paddingBottom:'40px'
									}}>
											<a className="exit text-right w-full text-[#333333]" onClick={logout}>
										{t('common.logout')}
										</a>
									<div style={{
									display: 'flex',
									flexDirection: 'column',
									alignItems: 'center'
									}} className="iconHeader bg-write">
									
									{userInfoString&&JSON.parse(userInfoString||'').userId&&<img src={avatar} onMouseEnter={()=>setArrow(true)} onMouseLeave={()=>setArrow(false)} style={{ width: '60px',height: "60px" }} onError={errorImg} alt="avatar" />}
										<span className="account">{userInfoString&&JSON.parse(userInfoString||'').userName}</span>
									</div>
									</div>
								}
								>
								<a href="#">
									<div className="img-area">
									{userInfoString&&JSON.parse(userInfoString||'').userId&&<img src={avatar} onMouseEnter={()=>setArrow(true)} onMouseLeave={()=>setArrow(false)} style={{ width: '30px',height: "32px" }} onError={errorImg} alt="avatar" />}
									</div>
								</a>
								</Popover>
						</div>
					</div>
				</div>

				{/* Main */}
				<div className="flex-1 overflow-hidden flex rounded-3xl bg-white">
					{appConfigLoading || initLoading ? (
						<div className="absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center">
							<Spin spinning />
						</div>
					) : appConfig ? (
						<>
							{/* 左侧对话列表 */}
							<div className={`${styles.menu} hidden md:!flex w-72 h-full flex-col`}>
								{/* 添加会话 */}
								{appConfig ? (
									<Button
										onClick={() => {
											resetFormValues()
											onAddConversation()
										}}
										className="h-10 leading-10 border border-solid border-gray-200 w-[calc(100%-24px)] mt-3 mx-3 text-default"
										icon={<PlusOutlined />}
									>
										{t('chat.newConversation')}
									</Button>
								) : null}
								{/* 🌟 对话管理 */}
								<div className="px-3 flex-1 overflow-y-auto">
									<Spin spinning={conversationListLoading}>
										{conversationsItems?.length ? (
											<ConversationList
												renameConversationPromise={(conversationId: string, name: string) =>
													difyApi?.renameConversation({
														conversation_id: conversationId,
														name,
													})
												}
												deleteConversationPromise={difyApi?.deleteConversation}
												items={conversationsItems}
												activeKey={currentConversationId}
												onActiveChange={id => {
													resetFormValues()
													setCurrentConversationId(id)
												}}
												onItemsChange={setConversationsItems}
												refreshItems={getConversationItems}
												appConfig={appConfig}
												onchangeModal2Open={(val)=>(setModal2Open(val))}
											/>
										) : (
											<div className="w-full h-full flex items-center justify-center">
												<Empty
													className="pt-6"
													description={t('chat.noConversations')}
												/>
											</div>
										)}
									</Spin>
								</div>
							</div>

							{/* 右侧聊天窗口 - 移动端全屏 */}
							<div className="flex-1 min-w-0 flex flex-col overflow-hidden">
							{conversationName?<ChatboxWrapper
									inputParams={inputParams}
									setInputParams={setInputParams}
									resetFormValues={resetFormValues}
									appConfig={appConfig}
									appConfigLoading={appConfigLoading}
									appInfo={appInfo}
									difyApi={difyApi}
									conversationId={currentConversationId}
									conversationName={conversationName}
									conversationItems={conversationsItems}
									onConversationIdChange={setCurrentConversationId}
									appParameters={appParameters}
									conversationListLoading={conversationListLoading}
									onAddConversation={onAddConversation}
									onItemsChange={setConversationsItems}
									conversationItemsChangeCallback={getConversationItems}
									modal2OpenF={modal2Open}
									onsubmit={(val:any)=>setOnSubmit(val)}
									subStatusDetails={subStatusDetail}
									changeSubStatusDetails={()=>{initSub()}}
									onClose={(val) => {
										setModal2Open(val)	
									}}
									selectedAppId={selectedAppId}
									appList={appList}
								/>:''}	
							</div>
						</>
					) : (
						<div className="w-full h-full flex items-center justify-center">
							<Empty
								description={t('app.noDifyConfig')}
								className="text-base"
							>
								<Button
									size="large"
									type="primary"
									onClick={handleStartConfig}
								>
									开始配置
								</Button>
							</Empty>
						</div>
					)}
				</div>
			</div>

			{extComponents}
		</XProvider>
	)
}

export default BaseLayout
