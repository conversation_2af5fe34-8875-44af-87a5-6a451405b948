// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 2025/7/19 11:54:43
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.js";
const config = [
{"content":{"files":["D:/job/main/home-tools/components/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/job/main/home-tools/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/job/main/home-tools/components/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/job/main/home-tools/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/job/main/home-tools/plugins/**/*.{js,ts,mjs}","D:/job/main/home-tools/composables/**/*.{js,ts,mjs}","D:/job/main/home-tools/utils/**/*.{js,ts,mjs}","D:/job/main/home-tools/pages/index.vue","D:/job/main/home-tools/pages/sign-up.vue","D:/job/main/home-tools/pages/login/index.vue","D:/job/main/home-tools/pages/article/[id].vue","D:/job/main/home-tools/pages/tool/destroy.vue","D:/job/main/home-tools/pages/cases/[caseId].vue","D:/job/main/home-tools/pages/chat/[appUuid].vue","D:/job/main/home-tools/pages/tool/[appUuid].vue","D:/job/main/home-tools/pages/write/[appUuid].vue","D:/job/main/home-tools/pages/payLink/[payInfo].vue","D:/job/main/home-tools/pages/login/[socialType].vue","D:/job/main/home-tools/pages/tool/privacy-policy.vue","D:/job/main/home-tools/pages/tool/components/InputField.vue","D:/job/main/home-tools/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","D:/job/main/home-tools/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","D:/job/main/home-tools/app.config.{js,ts,mjs}"]}},
{},
cfg2
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;