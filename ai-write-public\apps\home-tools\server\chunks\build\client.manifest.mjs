const client_manifest = {
  "_BEsI4pyh.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BEsI4pyh.js",
    "name": "index",
    "imports": [
      "_TOGsb9HZ.js",
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": [
      "index.CNboY1JO.css"
    ]
  },
  "index.CNboY1JO.css": {
    "file": "index.CNboY1JO.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "_CjUHxhbH.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CjUHxhbH.js",
    "name": "client-only",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_DJjw1KTv.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DJjw1KTv.js",
    "name": "index",
    "imports": [
      "_CjUHxhbH.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js",
      "_x_rD_Ya3.js"
    ],
    "css": [
      "index.BrTV2Fw4.css"
    ]
  },
  "index.BrTV2Fw4.css": {
    "file": "index.BrTV2Fw4.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "_DMAjf-Z3.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DMAjf-Z3.js",
    "name": "lang"
  },
  "_DPV8digk.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DPV8digk.js",
    "name": "ssr",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_DlAUqK2U.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DlAUqK2U.js",
    "name": "_plugin-vue_export-helper"
  },
  "_DoZnhJ1D.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DoZnhJ1D.js",
    "name": "v3",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_TOGsb9HZ.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "TOGsb9HZ.js",
    "name": "qrcode",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "assets": [
      "kefu.B84d3bIM.png",
      "qrcode.DKObs6E2.png"
    ]
  },
  "kefu.B84d3bIM.png": {
    "file": "kefu.B84d3bIM.png",
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png"
  },
  "qrcode.DKObs6E2.png": {
    "file": "qrcode.DKObs6E2.png",
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png"
  },
  "_index.BrTV2Fw4.css": {
    "resourceType": "style",
    "prefetch": true,
    "preload": true,
    "file": "index.BrTV2Fw4.css",
    "src": "_index.BrTV2Fw4.css"
  },
  "_index.CNboY1JO.css": {
    "resourceType": "style",
    "prefetch": true,
    "preload": true,
    "file": "index.CNboY1JO.css",
    "src": "_index.CNboY1JO.css"
  },
  "_x_rD_Ya3.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "x_rD_Ya3.js",
    "name": "interval"
  },
  "assets/imgs/Bitmap.png": {
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png",
    "file": "Bitmap.BV-s4dwZ.png",
    "src": "assets/imgs/Bitmap.png"
  },
  "assets/imgs/kefu.png": {
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png",
    "file": "kefu.B84d3bIM.png",
    "src": "assets/imgs/kefu.png"
  },
  "assets/imgs/qrcode.png": {
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png",
    "file": "qrcode.DKObs6E2.png",
    "src": "assets/imgs/qrcode.png"
  },
  "assets/imgs/right.png": {
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png",
    "file": "right.C7PU2hTH.png",
    "src": "assets/imgs/right.png"
  },
  "i18n.config.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "MY8nHssn.js",
    "name": "i18n.config",
    "src": "i18n.config.js",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "layouts/default.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "m0_u5HEk.js",
    "name": "default",
    "src": "layouts/default.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "default.q2gX9DjU.css": {
    "file": "default.q2gX9DjU.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "layouts/tools.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DsTTjL9j.js",
    "name": "tools",
    "src": "layouts/tools.vue",
    "isDynamicEntry": true,
    "imports": [
      "_CjUHxhbH.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_DPV8digk.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js",
      "_BEsI4pyh.js",
      "_TOGsb9HZ.js"
    ],
    "css": []
  },
  "tools.x4A8XtZE.css": {
    "file": "tools.x4A8XtZE.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "node_modules/highlight.js/es/index.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BeEQ5gKP.js",
    "name": "index",
    "src": "node_modules/highlight.js/es/index.js",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/highlight.js/styles/vs2015.css": {
    "resourceType": "style",
    "prefetch": true,
    "preload": true,
    "file": "vs2015.LLWOf3w5.css",
    "src": "node_modules/highlight.js/styles/vs2015.css"
  },
  "node_modules/nuxt/dist/app/components/error-404.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DDj6mLH_.js",
    "name": "error-404",
    "src": "node_modules/nuxt/dist/app/components/error-404.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js",
      "_DoZnhJ1D.js"
    ],
    "css": []
  },
  "error-404.aNCZ2L4y.css": {
    "file": "error-404.aNCZ2L4y.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "node_modules/nuxt/dist/app/components/error-500.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DC3EdMM8.js",
    "name": "error-500",
    "src": "node_modules/nuxt/dist/app/components/error-500.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "_DoZnhJ1D.js",
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "error-500.JESWioAZ.css": {
    "file": "error-500.JESWioAZ.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "node_modules/nuxt/dist/app/entry.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BHVVUzUw.js",
    "name": "entry",
    "src": "node_modules/nuxt/dist/app/entry.js",
    "isEntry": true,
    "isDynamicEntry": true,
    "dynamicImports": [
      "i18n.config.js",
      "layouts/default.vue",
      "layouts/tools.vue",
      "node_modules/nuxt/dist/app/components/error-404.vue",
      "node_modules/nuxt/dist/app/components/error-500.vue"
    ],
    "css": [
      "entry.BM64DQIl.css"
    ],
    "_globalCSS": true
  },
  "entry.BM64DQIl.css": {
    "file": "entry.BM64DQIl.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/article/[id].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DdujEBH1.js",
    "name": "_id_",
    "src": "pages/article/[id].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DPV8digk.js",
      "_DoZnhJ1D.js",
      "_DlAUqK2U.js"
    ],
    "dynamicImports": [
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "_id_.DmbhpAJy.css": {
    "file": "_id_.DmbhpAJy.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/cases/[caseId].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DC6qazml.js",
    "name": "_caseId_",
    "src": "pages/cases/[caseId].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js",
      "_DoZnhJ1D.js",
      "_DPV8digk.js"
    ],
    "dynamicImports": [
      "node_modules/highlight.js/es/index.js"
    ],
    "css": []
  },
  "_caseId_.CmqoPyH7.css": {
    "file": "_caseId_.CmqoPyH7.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/chat/[appUuid].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DHve64cQ.js",
    "name": "_appUuid_",
    "src": "pages/chat/[appUuid].vue",
    "isDynamicEntry": true,
    "imports": [
      "_TOGsb9HZ.js",
      "_DMAjf-Z3.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_DPV8digk.js",
      "_DoZnhJ1D.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "_appUuid_.BGANLTBm.css": {
    "file": "_appUuid_.BGANLTBm.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "jTKEPZXh.js",
    "name": "index",
    "src": "pages/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "_CjUHxhbH.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_DPV8digk.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js",
      "_BEsI4pyh.js",
      "_DJjw1KTv.js",
      "_DMAjf-Z3.js",
      "_DoZnhJ1D.js",
      "_TOGsb9HZ.js"
    ],
    "css": [],
    "assets": [
      "Bitmap.BV-s4dwZ.png"
    ]
  },
  "index.YYgUUJq8.css": {
    "file": "index.YYgUUJq8.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "Bitmap.BV-s4dwZ.png": {
    "file": "Bitmap.BV-s4dwZ.png",
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png"
  },
  "pages/login/[socialType].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CaROaodz.js",
    "name": "_socialType_",
    "src": "pages/login/[socialType].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "_socialType_.DYGiZ1nj.css": {
    "file": "_socialType_.DYGiZ1nj.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/login/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DTTpSsVL.js",
    "name": "index",
    "src": "pages/login/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "index.D0WfnBpn.css": {
    "file": "index.D0WfnBpn.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/payLink/[payInfo].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Cud1pDsC.js",
    "name": "_payInfo_",
    "src": "pages/payLink/[payInfo].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "_payInfo_.BM6XeFv8.css": {
    "file": "_payInfo_.BM6XeFv8.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/sign-up.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DIpxh5vw.js",
    "name": "sign-up",
    "src": "pages/sign-up.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "sign-up.Cj3pzlL2.css": {
    "file": "sign-up.Cj3pzlL2.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/tool/[appUuid].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DxH7vPLY.js",
    "name": "_appUuid_",
    "src": "pages/tool/[appUuid].vue",
    "isDynamicEntry": true,
    "imports": [
      "_CjUHxhbH.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_DJjw1KTv.js",
      "pages/tool/components/InputField.vue",
      "_DMAjf-Z3.js",
      "_DPV8digk.js",
      "_DoZnhJ1D.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "_appUuid_.D70b9I12.css": {
    "file": "_appUuid_.D70b9I12.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/tool/components/InputField.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "0icX25hh.js",
    "name": "InputField",
    "src": "pages/tool/components/InputField.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/tool/destroy.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "D6nFnn8c.js",
    "name": "destroy",
    "src": "pages/tool/destroy.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "destroy.BvPgb2nQ.css": {
    "file": "destroy.BvPgb2nQ.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/tool/privacy-policy.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BIYIzNZ-.js",
    "name": "privacy-policy",
    "src": "pages/tool/privacy-policy.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/write/[appUuid].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DsyG9kcv.js",
    "name": "_appUuid_",
    "src": "pages/write/[appUuid].vue",
    "isDynamicEntry": true,
    "imports": [
      "_TOGsb9HZ.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_DMAjf-Z3.js",
      "_DPV8digk.js",
      "_DoZnhJ1D.js",
      "_DlAUqK2U.js"
    ],
    "css": [],
    "assets": [
      "right.C7PU2hTH.png"
    ]
  },
  "_appUuid_.CUY9OiQN.css": {
    "file": "_appUuid_.CUY9OiQN.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "right.C7PU2hTH.png": {
    "file": "right.C7PU2hTH.png",
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png"
  }
};

export { client_manifest as default };
//# sourceMappingURL=client.manifest.mjs.map
