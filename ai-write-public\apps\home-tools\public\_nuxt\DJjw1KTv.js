import{_ as Bi}from"./CjUHxhbH.js";import{af as xt,an as Zt,aw as Pi,J as Ie,az as Tt,al as ut,j as we,r as H,o as Je,a5 as ct,ag as kt,ao as Un,L as C,au as $n,aA as Ai,z as A,d as ke,as as Ue,e as jn,aB as Hn,aC as Gn,aq as We,ar as Xe,M as _t,aD as Di,aE as Ei,t as M,K as z,v as D,D as Oi,u as Li,g as $t,aF as Mi,x as v,y as E,V as dn,N as jt,O as ye,B as W,U as Ne,A as be,aG as Vn,aH as Fi,aI as Kn,a0 as Ye,Q as Ni,a2 as Wn,aJ as Xn,aK as gt,a3 as fn,aL as zi,E as hn}from"./BHVVUzUw.js";import{_ as Jt}from"./DlAUqK2U.js";import{s as Yn}from"./x_rD_Ya3.js";function Ht(){}const pe=Object.assign,qt=typeof window<"u",Qt=t=>t!==null&&typeof t=="object",Me=t=>t!=null,St=t=>typeof t=="function",Ri=t=>Qt(t)&&St(t.then)&&St(t.catch),Zn=t=>typeof t=="number"||/^\d+(\.\d+)?$/.test(t),Ui=()=>qt?/ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase()):!1;function gn(t,e){const n=e.split(".");let i=t;return n.forEach(o=>{var s;i=Qt(i)&&(s=i[o])!=null?s:""}),i}function en(t,e,n){return e.reduce((i,o)=>(i[o]=t[o],i),{})}const $e=null,ae=[Number,String],me={type:Boolean,default:!0},$i=t=>({type:t,required:!0}),ji=t=>({type:Number,default:t}),bl=t=>({type:ae,default:t}),re=t=>({type:String,default:t});var qe=typeof window<"u";function mn(t){return qe?requestAnimationFrame(t):-1}function wl(t){qe&&cancelAnimationFrame(t)}function Cl(t){mn(()=>mn(t))}var Hi=t=>t===window,pn=(t,e)=>({top:0,left:0,right:t,bottom:e,width:t,height:e}),Jn=t=>{const e=C(t);if(Hi(e)){const n=e.innerWidth,i=e.innerHeight;return pn(n,i)}return e!=null&&e.getBoundingClientRect?e.getBoundingClientRect():pn(0,0)};function qn(t){const e=Tt(t,null);if(e){const n=ut(),{link:i,unlink:o,internalChildren:s}=e;i(n),xt(()=>o(n));const a=we(()=>s.indexOf(n));return{parent:e,index:a}}return{parent:null,index:H(-1)}}function Gi(t){const e=[],n=i=>{Array.isArray(i)&&i.forEach(o=>{var s;Ai(o)&&(e.push(o),(s=o.component)!=null&&s.subTree&&(e.push(o.component.subTree),n(o.component.subTree.children)),o.children&&n(o.children))})};return n(t),e}var vn=(t,e)=>{const n=t.indexOf(e);return n===-1?t.findIndex(i=>e.key!==void 0&&e.key!==null&&i.type===e.type&&i.key===e.key):n};function Vi(t,e,n){const i=Gi(t.subTree.children);n.sort((s,a)=>vn(i,s.vnode)-vn(i,a.vnode));const o=n.map(s=>s.proxy);e.sort((s,a)=>{const r=o.indexOf(s),l=o.indexOf(a);return r-l})}function Ki(t){const e=ct([]),n=ct([]),i=ut();return{children:e,linkChildren:s=>{$n(t,Object.assign({link:l=>{l.proxy&&(n.push(l),e.push(l.proxy),Vi(i,e,n))},unlink:l=>{const c=n.indexOf(l);e.splice(c,1),n.splice(c,1)},children:e,internalChildren:n},s))}}}function Qn(t){let e;Je(()=>{t(),kt(()=>{e=!0})}),Un(()=>{e&&t()})}function ei(t,e,n={}){if(!qe)return;const{target:i=window,passive:o=!1,capture:s=!1}=n;let a=!1,r;const l=u=>{if(a)return;const h=C(u);h&&!r&&(h.addEventListener(t,e,{capture:s,passive:o}),r=!0)},c=u=>{if(a)return;const h=C(u);h&&r&&(h.removeEventListener(t,e,s),r=!1)};xt(()=>c(i)),Zt(()=>c(i)),Qn(()=>l(i));let d;return Pi(i)&&(d=Ie(i,(u,h)=>{c(h),l(u)})),()=>{d==null||d(),c(i),a=!0}}var mt,Lt;function Wi(){if(!mt&&(mt=H(0),Lt=H(0),qe)){const t=()=>{mt.value=window.innerWidth,Lt.value=window.innerHeight};t(),window.addEventListener("resize",t,{passive:!0}),window.addEventListener("orientationchange",t,{passive:!0})}return{width:mt,height:Lt}}var Xi=/scroll|auto|overlay/i,ti=qe?window:void 0;function Yi(t){return t.tagName!=="HTML"&&t.tagName!=="BODY"&&t.nodeType===1}function ni(t,e=ti){let n=t;for(;n&&n!==e&&Yi(n);){const{overflowY:i}=window.getComputedStyle(n);if(Xi.test(i))return n;n=n.parentNode}return e}function kl(t,e=ti){const n=H();return Je(()=>{t.value&&(n.value=ni(t.value,e))}),n}var pt;function _l(){if(!pt&&(pt=H("visible"),qe)){const t=()=>{pt.value=document.hidden?"hidden":"visible"};t(),window.addEventListener("visibilitychange",t)}return pt}var Zi=Symbol("van-field");function Ji(t){const e=Tt(Zi,null);e&&!e.customValue.value&&(e.customValue.value=t,Ie(t,()=>{e.resetValidation(),e.validateWithTrigger("onChange")}))}function qi(t){const e="scrollTop"in t?t.scrollTop:t.pageYOffset;return Math.max(e,0)}function yn(t,e){"scrollTop"in t?t.scrollTop=e:t.scrollTo(t.scrollX,e)}function Qi(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0}function Sl(t){yn(window,t),yn(document.body,t)}function xl(t,e){if(t===window)return 0;const n=e?qi(e):Qi();return Jn(t).top+n}Ui();const eo=t=>t.stopPropagation();function tn(t,e){(typeof t.cancelable!="boolean"||t.cancelable)&&t.preventDefault(),e&&eo(t)}function Tl(t){const e=C(t);if(!e)return!1;const n=window.getComputedStyle(e),i=n.display==="none",o=e.offsetParent===null&&n.position!=="fixed";return i||o}const{width:ii,height:oi}=Wi();function ce(t){if(Me(t))return Zn(t)?`${t}px`:String(t)}function to(t){if(Me(t)){if(Array.isArray(t))return{width:ce(t[0]),height:ce(t[1])};const e=ce(t);return{width:e,height:e}}}function no(t){const e={};return t!==void 0&&(e.zIndex=+t),e}let Mt;function io(){if(!Mt){const t=document.documentElement,e=t.style.fontSize||window.getComputedStyle(t).fontSize;Mt=parseFloat(e)}return Mt}function oo(t){return t=t.replace(/rem/g,""),+t*io()}function so(t){return t=t.replace(/vw/g,""),+t*ii.value/100}function ao(t){return t=t.replace(/vh/g,""),+t*oi.value/100}function Il(t){if(typeof t=="number")return t;if(qt){if(t.includes("rem"))return oo(t);if(t.includes("vw"))return so(t);if(t.includes("vh"))return ao(t)}return parseFloat(t)}const ro=/-(\w)/g,si=t=>t.replace(ro,(e,n)=>n.toUpperCase()),Bl=(t,e,n)=>Math.min(Math.max(t,e),n),{hasOwnProperty:lo}=Object.prototype;function co(t,e,n){const i=e[n];Me(i)&&(!lo.call(t,n)||!Qt(i)?t[n]=i:t[n]=ai(Object(t[n]),i))}function ai(t,e){return Object.keys(e).forEach(n=>{co(t,e,n)}),t}var uo={name:"姓名",tel:"电话",save:"保存",clear:"清空",cancel:"取消",confirm:"确认",delete:"删除",loading:"加载中...",noCoupon:"暂无优惠券",nameEmpty:"请填写姓名",addContact:"添加联系人",telInvalid:"请填写正确的电话",vanCalendar:{end:"结束",start:"开始",title:"日期选择",weekdays:["日","一","二","三","四","五","六"],monthTitle:(t,e)=>`${t}年${e}月`,rangePrompt:t=>`最多选择 ${t} 天`},vanCascader:{select:"请选择"},vanPagination:{prev:"上一页",next:"下一页"},vanPullRefresh:{pulling:"下拉即可刷新...",loosing:"释放即可刷新..."},vanSubmitBar:{label:"合计:"},vanCoupon:{unlimited:"无门槛",discount:t=>`${t}折`,condition:t=>`满${t}元可用`},vanCouponCell:{title:"优惠券",count:t=>`${t}张可用`},vanCouponList:{exchange:"兑换",close:"不使用",enable:"可用",disabled:"不可用",placeholder:"输入优惠码"},vanAddressEdit:{area:"地区",areaEmpty:"请选择地区",addressEmpty:"请填写详细地址",addressDetail:"详细地址",defaultAddress:"设为默认收货地址"},vanAddressList:{add:"新增地址"}};const bn=H("zh-CN"),wn=ct({"zh-CN":uo}),fo={messages(){return wn[bn.value]},use(t,e){bn.value=t,this.add({[t]:e})},add(t={}){ai(wn,t)}};var ho=fo;function go(t){const e=si(t)+".";return(n,...i)=>{const o=ho.messages(),s=gn(o,e+n)||gn(o,n);return St(s)?s(...i):s}}function Gt(t,e){return e?typeof e=="string"?` ${t}--${e}`:Array.isArray(e)?e.reduce((n,i)=>n+Gt(t,i),""):Object.keys(e).reduce((n,i)=>n+(e[i]?Gt(t,i):""),""):""}function mo(t){return(e,n)=>(e&&typeof e!="string"&&(n=e,e=""),e=e?`${t}__${e}`:t,`${e}${Gt(e,n)}`)}function ve(t){const e=`van-${t}`;return[e,mo(e),go(e)]}const It="van-hairline",po=`${It}--top`,vo=`${It}--left`,yo=`${It}--surround`,Pl=`${It}--top-bottom`,bo="van-haptics-feedback",Cn=5;function ri(t,{args:e=[],done:n,canceled:i,error:o}){if(t){const s=t.apply(null,e);Ri(s)?s.then(a=>{a?n():i&&i()}).catch(o||Ht):s?n():i&&i()}else n()}function Se(t){return t.install=e=>{const{name:n}=t;n&&(e.component(n,t),e.component(si(`-${n}`),t))},t}const li=Symbol();function wo(t){const e=Tt(li,null);e&&Ie(e,n=>{n&&t()})}const Co=(t,e)=>{const n=H(),i=()=>{n.value=Jn(t).height};return Je(()=>{kt(i);for(let o=1;o<=3;o++)setTimeout(i,100*o)}),wo(()=>kt(i)),Ie([ii,oi],i),n};function ko(t,e){const n=Co(t);return i=>A("div",{class:e("placeholder"),style:{height:n.value?`${n.value}px`:void 0}},[i()])}const[ci,kn]=ve("action-bar"),ui=Symbol(ci),_o={placeholder:Boolean,safeAreaInsetBottom:me};var So=ke({name:ci,props:_o,setup(t,{slots:e}){const n=H(),i=ko(n,kn),{linkChildren:o}=Ki(ui);o();const s=()=>{var a;return A("div",{ref:n,class:[kn(),{"van-safe-area-bottom":t.safeAreaInsetBottom}]},[(a=e.default)==null?void 0:a.call(e)])};return()=>t.placeholder?i(s):s()}});const xo=Se(So);function Bt(t){const e=ut();e&&pe(e.proxy,t)}const di={to:[String,Object],url:String,replace:Boolean};function To({to:t,url:e,replace:n,$router:i}){t&&i?i[n?"replace":"push"](t):e&&(n?location.replace(e):location.href=e)}function fi(){const t=ut().proxy;return()=>To(t)}const[Io,_n]=ve("badge"),Bo={dot:Boolean,max:ae,tag:re("div"),color:String,offset:Array,content:ae,showZero:me,position:re("top-right")};var Po=ke({name:Io,props:Bo,setup(t,{slots:e}){const n=()=>{if(e.content)return!0;const{content:r,showZero:l}=t;return Me(r)&&r!==""&&(l||r!==0&&r!=="0")},i=()=>{const{dot:r,max:l,content:c}=t;if(!r&&n())return e.content?e.content():Me(l)&&Zn(c)&&+c>+l?`${l}+`:c},o=r=>r.startsWith("-")?r.replace("-",""):`-${r}`,s=we(()=>{const r={background:t.color};if(t.offset){const[l,c]=t.offset,{position:d}=t,[u,h]=d.split("-");e.default?(typeof c=="number"?r[u]=ce(u==="top"?c:-c):r[u]=u==="top"?ce(c):o(c),typeof l=="number"?r[h]=ce(h==="left"?l:-l):r[h]=h==="left"?ce(l):o(l)):(r.marginTop=ce(c),r.marginLeft=ce(l))}return r}),a=()=>{if(n()||t.dot)return A("div",{class:_n([t.position,{dot:t.dot,fixed:!!e.default}]),style:s.value},[i()])};return()=>{if(e.default){const{tag:r}=t;return A(r,{class:_n("wrapper")},{default:()=>[e.default(),a()]})}return a()}}});const Ao=Se(Po);let Do=2e3;const Eo=()=>++Do,[Oo,Al]=ve("config-provider"),Lo=Symbol(Oo),[Mo,Sn]=ve("icon"),Fo=t=>t==null?void 0:t.includes("/"),No={dot:Boolean,tag:re("i"),name:String,size:ae,badge:ae,color:String,badgeProps:Object,classPrefix:String};var zo=ke({name:Mo,props:No,setup(t,{slots:e}){const n=Tt(Lo,null),i=we(()=>t.classPrefix||(n==null?void 0:n.iconPrefix)||Sn());return()=>{const{tag:o,dot:s,name:a,size:r,badge:l,color:c}=t,d=Fo(a);return A(Ao,Ue({dot:s,tag:o,class:[i.value,d?"":`${i.value}-${a}`],style:{color:c,fontSize:ce(r)},content:l},t.badgeProps),{default:()=>{var u;return[(u=e.default)==null?void 0:u.call(e),d&&A("img",{class:Sn("image"),src:a},null)]}})}}});const Pt=Se(zo),[Ro,lt]=ve("loading"),Uo=Array(12).fill(null).map((t,e)=>A("i",{class:lt("line",String(e+1))},null)),$o=A("svg",{class:lt("circular"),viewBox:"25 25 50 50"},[A("circle",{cx:"50",cy:"50",r:"20",fill:"none"},null)]),jo={size:ae,type:re("circular"),color:String,vertical:Boolean,textSize:ae,textColor:String};var Ho=ke({name:Ro,props:jo,setup(t,{slots:e}){const n=we(()=>pe({color:t.color},to(t.size))),i=()=>{const s=t.type==="spinner"?Uo:$o;return A("span",{class:lt("spinner",t.type),style:n.value},[e.icon?e.icon():s])},o=()=>{var s;if(e.default)return A("span",{class:lt("text"),style:{fontSize:ce(t.textSize),color:(s=t.textColor)!=null?s:t.color}},[e.default()])};return()=>{const{type:s,vertical:a}=t;return A("div",{class:lt([s,{vertical:a}]),"aria-live":"polite","aria-busy":!0},[i(),o()])}}});const hi=Se(Ho),[Go,Ge]=ve("button"),Vo=pe({},di,{tag:re("button"),text:String,icon:String,type:re("default"),size:re("normal"),color:String,block:Boolean,plain:Boolean,round:Boolean,square:Boolean,loading:Boolean,hairline:Boolean,disabled:Boolean,iconPrefix:String,nativeType:re("button"),loadingSize:ae,loadingText:String,loadingType:String,iconPosition:re("left")});var Ko=ke({name:Go,props:Vo,emits:["click"],setup(t,{emit:e,slots:n}){const i=fi(),o=()=>n.loading?n.loading():A(hi,{size:t.loadingSize,type:t.loadingType,class:Ge("loading")},null),s=()=>{if(t.loading)return o();if(n.icon)return A("div",{class:Ge("icon")},[n.icon()]);if(t.icon)return A(Pt,{name:t.icon,class:Ge("icon"),classPrefix:t.iconPrefix},null)},a=()=>{let c;if(t.loading?c=t.loadingText:c=n.default?n.default():t.text,c)return A("span",{class:Ge("text")},[c])},r=()=>{const{color:c,plain:d}=t;if(c){const u={color:d?c:"white"};return d||(u.background=c),c.includes("gradient")?u.border=0:u.borderColor=c,u}},l=c=>{t.loading?tn(c):t.disabled||(e("click",c),i())};return()=>{const{tag:c,type:d,size:u,block:h,round:k,plain:x,square:I,loading:L,disabled:g,hairline:f,nativeType:T,iconPosition:y}=t,_=[Ge([d,u,{plain:x,block:h,round:k,square:I,loading:L,disabled:g,hairline:f}]),{[yo]:f}];return A(c,{type:T,class:_,style:r(),disabled:g,onClick:l},{default:()=>[A("div",{class:Ge("content")},[y==="left"&&s(),a(),y==="right"&&s()])]})}}});const Vt=Se(Ko),[Wo,Xo]=ve("action-bar-button"),Yo=pe({},di,{type:String,text:String,icon:String,color:String,loading:Boolean,disabled:Boolean});var Zo=ke({name:Wo,props:Yo,setup(t,{slots:e}){const n=fi(),{parent:i,index:o}=qn(ui),s=we(()=>{if(i){const r=i.children[o.value-1];return!(r&&"isButton"in r)}}),a=we(()=>{if(i){const r=i.children[o.value+1];return!(r&&"isButton"in r)}});return Bt({isButton:!0}),()=>{const{type:r,icon:l,text:c,color:d,loading:u,disabled:h}=t;return A(Vt,{class:Xo([r,{last:a.value,first:s.value}]),size:"large",type:r,icon:l,color:d,loading:u,disabled:h,onClick:n},{default:()=>[e.default?e.default():c]})}}});const xn=Se(Zo),nn={show:Boolean,zIndex:ae,overlay:me,duration:ae,teleport:[String,Object],lockScroll:me,lazyRender:me,beforeClose:Function,overlayStyle:Object,overlayClass:$e,transitionAppear:Boolean,closeOnClickOverlay:me},Jo=Object.keys(nn);function qo(t,e){return t>e?"horizontal":e>t?"vertical":""}function Qo(){const t=H(0),e=H(0),n=H(0),i=H(0),o=H(0),s=H(0),a=H(""),r=H(!0),l=()=>a.value==="vertical",c=()=>a.value==="horizontal",d=()=>{n.value=0,i.value=0,o.value=0,s.value=0,a.value="",r.value=!0};return{move:k=>{const x=k.touches[0];n.value=(x.clientX<0?0:x.clientX)-t.value,i.value=x.clientY-e.value,o.value=Math.abs(n.value),s.value=Math.abs(i.value);const I=10;(!a.value||o.value<I&&s.value<I)&&(a.value=qo(o.value,s.value)),r.value&&(o.value>Cn||s.value>Cn)&&(r.value=!1)},start:k=>{d(),t.value=k.touches[0].clientX,e.value=k.touches[0].clientY},reset:d,startX:t,startY:e,deltaX:n,deltaY:i,offsetX:o,offsetY:s,direction:a,isVertical:l,isHorizontal:c,isTap:r}}let ot=0;const Tn="van-overflow-hidden";function es(t,e){const n=Qo(),i="01",o="10",s=d=>{n.move(d);const u=n.deltaY.value>0?o:i,h=ni(d.target,t.value),{scrollHeight:k,offsetHeight:x,scrollTop:I}=h;let L="11";I===0?L=x>=k?"00":"01":I+x>=k&&(L="10"),L!=="11"&&n.isVertical()&&!(parseInt(L,2)&parseInt(u,2))&&tn(d,!0)},a=()=>{document.addEventListener("touchstart",n.start),document.addEventListener("touchmove",s,{passive:!1}),ot||document.body.classList.add(Tn),ot++},r=()=>{ot&&(document.removeEventListener("touchstart",n.start),document.removeEventListener("touchmove",s),ot--,ot||document.body.classList.remove(Tn))},l=()=>e()&&a(),c=()=>e()&&r();Qn(l),Zt(c),jn(c),Ie(e,d=>{d?a():r()})}function gi(t){const e=H(!1);return Ie(t,n=>{n&&(e.value=n)},{immediate:!0}),n=>()=>e.value?n():null}const In=()=>{var t;const{scopeId:e}=((t=ut())==null?void 0:t.vnode)||{};return e?{[e]:""}:null},[ts,ns]=ve("overlay"),is={show:Boolean,zIndex:ae,duration:ae,className:$e,lockScroll:me,lazyRender:me,customStyle:Object,teleport:[String,Object]};var os=ke({name:ts,props:is,setup(t,{slots:e}){const n=H(),i=gi(()=>t.show||!t.lazyRender),o=a=>{t.lockScroll&&tn(a,!0)},s=i(()=>{var a;const r=pe(no(t.zIndex),t.customStyle);return Me(t.duration)&&(r.animationDuration=`${t.duration}s`),We(A("div",{ref:n,style:r,class:[ns(),t.className]},[(a=e.default)==null?void 0:a.call(e)]),[[Xe,t.show]])});return ei("touchmove",o,{target:n}),()=>{const a=A(Hn,{name:"van-fade",appear:!0},{default:s});return t.teleport?A(Gn,{to:t.teleport},{default:()=>[a]}):a}}});const ss=Se(os),as=pe({},nn,{round:Boolean,position:re("center"),closeIcon:re("cross"),closeable:Boolean,transition:String,iconPrefix:String,closeOnPopstate:Boolean,closeIconPosition:re("top-right"),destroyOnClose:Boolean,safeAreaInsetTop:Boolean,safeAreaInsetBottom:Boolean}),[rs,Bn]=ve("popup");var ls=ke({name:rs,inheritAttrs:!1,props:as,emits:["open","close","opened","closed","keydown","update:show","clickOverlay","clickCloseIcon"],setup(t,{emit:e,attrs:n,slots:i}){let o,s;const a=H(),r=H(),l=gi(()=>t.show||!t.lazyRender),c=we(()=>{const b={zIndex:a.value};if(Me(t.duration)){const m=t.position==="center"?"animationDuration":"transitionDuration";b[m]=`${t.duration}s`}return b}),d=()=>{o||(o=!0,a.value=t.zIndex!==void 0?+t.zIndex:Eo(),e("open"))},u=()=>{o&&ri(t.beforeClose,{done(){o=!1,e("close"),e("update:show",!1)}})},h=b=>{e("clickOverlay",b),t.closeOnClickOverlay&&u()},k=()=>{if(t.overlay)return A(ss,Ue({show:t.show,class:t.overlayClass,zIndex:a.value,duration:t.duration,customStyle:t.overlayStyle,role:t.closeOnClickOverlay?"button":void 0,tabindex:t.closeOnClickOverlay?0:void 0},In(),{onClick:h}),{default:i["overlay-content"]})},x=b=>{e("clickCloseIcon",b),u()},I=()=>{if(t.closeable)return A(Pt,{role:"button",tabindex:0,name:t.closeIcon,class:[Bn("close-icon",t.closeIconPosition),bo],classPrefix:t.iconPrefix,onClick:x},null)};let L;const g=()=>{L&&clearTimeout(L),L=setTimeout(()=>{e("opened")})},f=()=>e("closed"),T=b=>e("keydown",b),y=l(()=>{var b;const{destroyOnClose:m,round:S,position:F,safeAreaInsetTop:$,safeAreaInsetBottom:p,show:j}=t;if(!(!j&&m))return We(A("div",Ue({ref:r,style:c.value,role:"dialog",tabindex:0,class:[Bn({round:S,[F]:F}),{"van-safe-area-top":$,"van-safe-area-bottom":p}],onKeydown:T},n,In()),[(b=i.default)==null?void 0:b.call(i),I()]),[[Xe,j]])}),_=()=>{const{position:b,transition:m,transitionAppear:S}=t,F=b==="center"?"van-fade":`van-popup-slide-${b}`;return A(Hn,{name:m||F,appear:S,onAfterEnter:g,onAfterLeave:f},{default:y})};return Ie(()=>t.show,b=>{b&&!o&&(d(),n.tabindex===0&&kt(()=>{var m;(m=r.value)==null||m.focus()})),!b&&o&&(o=!1,e("close"))}),Bt({popupRef:r}),es(r,()=>t.show&&t.lockScroll),ei("popstate",()=>{t.closeOnPopstate&&(u(),s=!1)}),Je(()=>{t.show&&d()}),Un(()=>{s&&(e("update:show",!0),s=!1)}),Zt(()=>{t.show&&t.teleport&&(u(),s=!0)}),$n(li,()=>t.show),()=>t.teleport?A(Gn,{to:t.teleport},{default:()=>[k(),_()]}):A(_t,null,[k(),_()])}});const mi=Se(ls);let st=0;function cs(t){t?(st||document.body.classList.add("van-toast--unclickable"),st++):st&&(st--,st||document.body.classList.remove("van-toast--unclickable"))}const[us,Ve]=ve("toast"),ds=["show","overlay","teleport","transition","overlayClass","overlayStyle","closeOnClickOverlay","zIndex"],fs={icon:String,show:Boolean,type:re("text"),overlay:Boolean,message:ae,iconSize:ae,duration:ji(2e3),position:re("middle"),teleport:[String,Object],wordBreak:String,className:$e,iconPrefix:String,transition:re("van-fade"),loadingType:String,forbidClick:Boolean,overlayClass:$e,overlayStyle:Object,closeOnClick:Boolean,closeOnClickOverlay:Boolean,zIndex:ae};var hs=ke({name:us,props:fs,emits:["update:show"],setup(t,{emit:e,slots:n}){let i,o=!1;const s=()=>{const u=t.show&&t.forbidClick;o!==u&&(o=u,cs(o))},a=u=>e("update:show",u),r=()=>{t.closeOnClick&&a(!1)},l=()=>clearTimeout(i),c=()=>{const{icon:u,type:h,iconSize:k,iconPrefix:x,loadingType:I}=t;if(u||h==="success"||h==="fail")return A(Pt,{name:u||h,size:k,class:Ve("icon"),classPrefix:x},null);if(h==="loading")return A(hi,{class:Ve("loading"),size:k,type:I},null)},d=()=>{const{type:u,message:h}=t;if(n.message)return A("div",{class:Ve("text")},[n.message()]);if(Me(h)&&h!=="")return u==="html"?A("div",{key:0,class:Ve("text"),innerHTML:String(h)},null):A("div",{class:Ve("text")},[h])};return Ie(()=>[t.show,t.forbidClick],s),Ie(()=>[t.show,t.type,t.message,t.duration],()=>{l(),t.show&&t.duration>0&&(i=setTimeout(()=>{a(!1)},t.duration))}),Je(s),xt(s),()=>A(mi,Ue({class:[Ve([t.position,t.wordBreak==="normal"?"break-normal":t.wordBreak,{[t.type]:!t.icon}]),t.className],lockScroll:!1,onClick:r,onClosed:l,"onUpdate:show":a},en(t,ds)),{default:()=>[c(),d()]})}});function gs(){const t=ct({show:!1}),e=o=>{t.show=o},n=o=>{pe(t,o,{transitionAppear:!0}),e(!0)},i=()=>e(!1);return Bt({open:n,close:i,toggle:e}),{open:n,close:i,state:t,toggle:e}}function ms(t){const e=Di(t),n=document.createElement("div");return document.body.appendChild(n),{instance:e.mount(n),unmount(){e.unmount(),document.body.removeChild(n)}}}const ps=Se(hs),[vs,Dl]=ve("checkbox-group"),ys=Symbol(vs),pi={name:$e,disabled:Boolean,iconSize:ae,modelValue:$e,checkedColor:String,labelPosition:String,labelDisabled:Boolean};var bs=ke({props:pe({},pi,{bem:$i(Function),role:String,shape:String,parent:Object,checked:Boolean,bindGroup:me,indeterminate:{type:Boolean,default:null}}),emits:["click","toggle"],setup(t,{emit:e,slots:n}){const i=H(),o=h=>{if(t.parent&&t.bindGroup)return t.parent.props[h]},s=we(()=>{if(t.parent&&t.bindGroup){const h=o("disabled")||t.disabled;if(t.role==="checkbox"){const k=o("modelValue").length,x=o("max"),I=x&&k>=+x;return h||I&&!t.checked}return h}return t.disabled}),a=we(()=>o("direction")),r=we(()=>{const h=t.checkedColor||o("checkedColor");if(h&&t.checked&&!s.value)return{borderColor:h,backgroundColor:h}}),l=we(()=>t.shape||o("shape")||"round"),c=h=>{const{target:k}=h,x=i.value,I=x===k||(x==null?void 0:x.contains(k));!s.value&&(I||!t.labelDisabled)&&e("toggle"),e("click",h)},d=()=>{var h,k;const{bem:x,checked:I,indeterminate:L}=t,g=t.iconSize||o("iconSize");return A("div",{ref:i,class:x("icon",[l.value,{disabled:s.value,checked:I,indeterminate:L}]),style:l.value!=="dot"?{fontSize:ce(g)}:{width:ce(g),height:ce(g),borderColor:(h=r.value)==null?void 0:h.borderColor}},[n.icon?n.icon({checked:I,disabled:s.value}):l.value!=="dot"?A(Pt,{name:L?"minus":"success",style:r.value},null):A("div",{class:x("icon--dot__icon"),style:{backgroundColor:(k=r.value)==null?void 0:k.backgroundColor}},null)])},u=()=>{const{checked:h}=t;if(n.default)return A("span",{class:t.bem("label",[t.labelPosition,{disabled:s.value}])},[n.default({checked:h,disabled:s.value})])};return()=>{const h=t.labelPosition==="left"?[u(),d()]:[d(),u()];return A("div",{role:t.role,class:t.bem([{disabled:s.value,"label-disabled":t.labelDisabled},a.value]),tabindex:s.value?void 0:0,"aria-checked":t.checked,onClick:c},[h])}}});const[ws,Cs]=ve("checkbox"),ks=pe({},pi,{shape:String,bindGroup:me,indeterminate:{type:Boolean,default:null}});var _s=ke({name:ws,props:ks,emits:["change","update:modelValue"],setup(t,{emit:e,slots:n}){const{parent:i}=qn(ys),o=r=>{const{name:l}=t,{max:c,modelValue:d}=i.props,u=d.slice();if(r)!(c&&u.length>=+c)&&!u.includes(l)&&(u.push(l),t.bindGroup&&i.updateValue(u));else{const h=u.indexOf(l);h!==-1&&(u.splice(h,1),t.bindGroup&&i.updateValue(u))}},s=we(()=>i&&t.bindGroup?i.props.modelValue.indexOf(t.name)!==-1:!!t.modelValue),a=(r=!s.value)=>{i&&t.bindGroup?o(r):e("update:modelValue",r),t.indeterminate!==null&&e("change",r)};return Ie(()=>t.modelValue,r=>{t.indeterminate===null&&e("change",r)}),Bt({toggle:a,props:t,checked:s}),Ji(()=>t.modelValue),()=>A(bs,Ue({bem:Cs,role:"checkbox",parent:i,checked:s.value,onToggle:a},t),en(n,["default","icon"]))}});const Ss=Se(_s),[xs,_e,vt]=ve("dialog"),Ts=pe({},nn,{title:String,theme:String,width:ae,message:[String,Function],callback:Function,allowHtml:Boolean,className:$e,transition:re("van-dialog-bounce"),messageAlign:String,closeOnPopstate:me,showCancelButton:Boolean,cancelButtonText:String,cancelButtonColor:String,cancelButtonDisabled:Boolean,confirmButtonText:String,confirmButtonColor:String,confirmButtonDisabled:Boolean,showConfirmButton:me,closeOnClickOverlay:Boolean,keyboardEnabled:me,destroyOnClose:Boolean}),Is=[...Jo,"transition","closeOnPopstate","destroyOnClose"];var vi=ke({name:xs,props:Ts,emits:["confirm","cancel","keydown","update:show"],setup(t,{emit:e,slots:n}){const i=H(),o=ct({confirm:!1,cancel:!1}),s=g=>e("update:show",g),a=g=>{var f;s(!1),(f=t.callback)==null||f.call(t,g)},r=g=>()=>{t.show&&(e(g),t.beforeClose?(o[g]=!0,ri(t.beforeClose,{args:[g],done(){a(g),o[g]=!1},canceled(){o[g]=!1}})):a(g))},l=r("cancel"),c=r("confirm"),d=Ei(g=>{var f,T;if(!t.keyboardEnabled||g.target!==((T=(f=i.value)==null?void 0:f.popupRef)==null?void 0:T.value))return;({Enter:t.showConfirmButton?c:Ht,Escape:t.showCancelButton?l:Ht})[g.key](),e("keydown",g)},["enter","esc"]),u=()=>{const g=n.title?n.title():t.title;if(g)return A("div",{class:_e("header",{isolated:!t.message&&!n.default})},[g])},h=g=>{const{message:f,allowHtml:T,messageAlign:y}=t,_=_e("message",{"has-title":g,[y]:y}),b=St(f)?f():f;return T&&typeof b=="string"?A("div",{class:_,innerHTML:b},null):A("div",{class:_},[b])},k=()=>{if(n.default)return A("div",{class:_e("content")},[n.default()]);const{title:g,message:f,allowHtml:T}=t;if(f){const y=!!(g||n.title);return A("div",{key:T?1:0,class:_e("content",{isolated:!y})},[h(y)])}},x=()=>A("div",{class:[po,_e("footer")]},[t.showCancelButton&&A(Vt,{size:"large",text:t.cancelButtonText||vt("cancel"),class:_e("cancel"),style:{color:t.cancelButtonColor},loading:o.cancel,disabled:t.cancelButtonDisabled,onClick:l},null),t.showConfirmButton&&A(Vt,{size:"large",text:t.confirmButtonText||vt("confirm"),class:[_e("confirm"),{[vo]:t.showCancelButton}],style:{color:t.confirmButtonColor},loading:o.confirm,disabled:t.confirmButtonDisabled,onClick:c},null)]),I=()=>A(xo,{class:_e("footer")},{default:()=>[t.showCancelButton&&A(xn,{type:"warning",text:t.cancelButtonText||vt("cancel"),class:_e("cancel"),color:t.cancelButtonColor,loading:o.cancel,disabled:t.cancelButtonDisabled,onClick:l},null),t.showConfirmButton&&A(xn,{type:"danger",text:t.confirmButtonText||vt("confirm"),class:_e("confirm"),color:t.confirmButtonColor,loading:o.confirm,disabled:t.confirmButtonDisabled,onClick:c},null)]}),L=()=>n.footer?n.footer():t.theme==="round-button"?I():x();return()=>{const{width:g,title:f,theme:T,message:y,className:_}=t;return A(mi,Ue({ref:i,role:"dialog",class:[_e([T]),_],style:{width:ce(g)},tabindex:0,"aria-labelledby":f||y,onKeydown:d,"onUpdate:show":s},en(t,Is)),{default:()=>[u(),k(),L()]})}}});let Kt;const Bs={title:"",width:"",theme:null,message:"",overlay:!0,callback:null,teleport:"body",className:"",allowHtml:!1,lockScroll:!0,transition:void 0,beforeClose:null,overlayClass:"",overlayStyle:void 0,messageAlign:"",cancelButtonText:"",cancelButtonColor:null,cancelButtonDisabled:!1,confirmButtonText:"",confirmButtonColor:null,confirmButtonDisabled:!1,showConfirmButton:!0,showCancelButton:!1,closeOnPopstate:!0,closeOnClickOverlay:!1,destroyOnClose:!1};let Ps=pe({},Bs);function As(){({instance:Kt}=ms({setup(){const{state:e,toggle:n}=gs();return()=>A(vi,Ue(e,{"onUpdate:show":n}),null)}}))}function Ds(t){return qt?new Promise((e,n)=>{Kt||As(),Kt.open(pe({},Ps,t,{callback:i=>{(i==="confirm"?e:n)(i)}}))}):Promise.resolve(void 0)}const Wt=t=>Ds(pe({showCancelButton:!0},t));Se(vi);function Ft(t){return t===""?t:t==="true"||t=="1"}function Es(t,e){return new Promise((n,i)=>{var o=new XMLHttpRequest;o.responseType="blob",o.onload=function(){var s=new FileReader;s.onloadend=function(){n(s.result)},s.readAsArrayBuffer(o.response)},o.open("GET",t),o.send()})}function xe(t){if(typeof t!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(t))}function Pn(t,e){for(var n="",i=0,o=-1,s=0,a,r=0;r<=t.length;++r){if(r<t.length)a=t.charCodeAt(r);else{if(a===47)break;a=47}if(a===47){if(!(o===r-1||s===1))if(o!==r-1&&s===2){if(n.length<2||i!==2||n.charCodeAt(n.length-1)!==46||n.charCodeAt(n.length-2)!==46){if(n.length>2){var l=n.lastIndexOf("/");if(l!==n.length-1){l===-1?(n="",i=0):(n=n.slice(0,l),i=n.length-1-n.lastIndexOf("/")),o=r,s=0;continue}}else if(n.length===2||n.length===1){n="",i=0,o=r,s=0;continue}}e&&(n.length>0?n+="/..":n="..",i=2)}else n.length>0?n+="/"+t.slice(o+1,r):n=t.slice(o+1,r),i=r-o-1;o=r,s=0}else a===46&&s!==-1?++s:s=-1}return n}function Os(t,e){var n=e.dir||e.root,i=e.base||(e.name||"")+(e.ext||"");return n?n===e.root?n+i:n+t+i:i}var Re={resolve:function(){for(var e="",n=!1,i,o=arguments.length-1;o>=-1&&!n;o--){var s;o>=0?s=arguments[o]:(i===void 0&&(i=process.cwd()),s=i),xe(s),s.length!==0&&(e=s+"/"+e,n=s.charCodeAt(0)===47)}return e=Pn(e,!n),n?e.length>0?"/"+e:"/":e.length>0?e:"."},normalize:function(e){if(xe(e),e.length===0)return".";var n=e.charCodeAt(0)===47,i=e.charCodeAt(e.length-1)===47;return e=Pn(e,!n),e.length===0&&!n&&(e="."),e.length>0&&i&&(e+="/"),n?"/"+e:e},isAbsolute:function(e){return xe(e),e.length>0&&e.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var e,n=0;n<arguments.length;++n){var i=arguments[n];xe(i),i.length>0&&(e===void 0?e=i:e+="/"+i)}return e===void 0?".":Re.normalize(e)},relative:function(e,n){if(xe(e),xe(n),e===n||(e=Re.resolve(e),n=Re.resolve(n),e===n))return"";for(var i=1;i<e.length&&e.charCodeAt(i)===47;++i);for(var o=e.length,s=o-i,a=1;a<n.length&&n.charCodeAt(a)===47;++a);for(var r=n.length,l=r-a,c=s<l?s:l,d=-1,u=0;u<=c;++u){if(u===c){if(l>c){if(n.charCodeAt(a+u)===47)return n.slice(a+u+1);if(u===0)return n.slice(a+u)}else s>c&&(e.charCodeAt(i+u)===47?d=u:u===0&&(d=0));break}var h=e.charCodeAt(i+u),k=n.charCodeAt(a+u);if(h!==k)break;h===47&&(d=u)}var x="";for(u=i+d+1;u<=o;++u)(u===o||e.charCodeAt(u)===47)&&(x.length===0?x+="..":x+="/..");return x.length>0?x+n.slice(a+d):(a+=d,n.charCodeAt(a)===47&&++a,n.slice(a))},_makeLong:function(e){return e},dirname:function(e){if(xe(e),e.length===0)return".";for(var n=e.charCodeAt(0),i=n===47,o=-1,s=!0,a=e.length-1;a>=1;--a)if(n=e.charCodeAt(a),n===47){if(!s){o=a;break}}else s=!1;return o===-1?i?"/":".":i&&o===1?"//":e.slice(0,o)},basename:function(e,n){if(n!==void 0&&typeof n!="string")throw new TypeError('"ext" argument must be a string');xe(e);var i=0,o=-1,s=!0,a;if(n!==void 0&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return"";var r=n.length-1,l=-1;for(a=e.length-1;a>=0;--a){var c=e.charCodeAt(a);if(c===47){if(!s){i=a+1;break}}else l===-1&&(s=!1,l=a+1),r>=0&&(c===n.charCodeAt(r)?--r===-1&&(o=a):(r=-1,o=l))}return i===o?o=l:o===-1&&(o=e.length),e.slice(i,o)}else{for(a=e.length-1;a>=0;--a)if(e.charCodeAt(a)===47){if(!s){i=a+1;break}}else o===-1&&(s=!1,o=a+1);return o===-1?"":e.slice(i,o)}},extname:function(e){xe(e);for(var n=-1,i=0,o=-1,s=!0,a=0,r=e.length-1;r>=0;--r){var l=e.charCodeAt(r);if(l===47){if(!s){i=r+1;break}continue}o===-1&&(s=!1,o=r+1),l===46?n===-1?n=r:a!==1&&(a=1):n!==-1&&(a=-1)}return n===-1||o===-1||a===0||a===1&&n===o-1&&n===i+1?"":e.slice(n,o)},format:function(e){if(e===null||typeof e!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return Os("/",e)},parse:function(e){xe(e);var n={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return n;var i=e.charCodeAt(0),o=i===47,s;o?(n.root="/",s=1):s=0;for(var a=-1,r=0,l=-1,c=!0,d=e.length-1,u=0;d>=s;--d){if(i=e.charCodeAt(d),i===47){if(!c){r=d+1;break}continue}l===-1&&(c=!1,l=d+1),i===46?a===-1?a=d:u!==1&&(u=1):a!==-1&&(u=-1)}return a===-1||l===-1||u===0||u===1&&a===l-1&&a===r+1?l!==-1&&(r===0&&o?n.base=n.name=e.slice(1,l):n.base=n.name=e.slice(r,l)):(r===0&&o?(n.name=e.slice(1,a),n.base=e.slice(1,l)):(n.name=e.slice(r,a),n.base=e.slice(r,l)),n.ext=e.slice(a,l)),r>0?n.dir=e.slice(0,r-1):o&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};Re.posix=Re;const Ls=Re.extname,yi=Re.basename;class Ms{constructor(){let e=typeof global>"u",n="image/png",i="image/jpeg",o="image/jpeg",s="image/webp",a="application/pdf",r="image/svg+xml";Object.assign(this,{toMime:this.toMime.bind(this),fromMime:this.fromMime.bind(this),expected:e?'"png", "jpg", or "webp"':'"png", "jpg", "pdf", or "svg"',formats:e?{png:n,jpg:i,jpeg:o,webp:s}:{png:n,jpg:i,jpeg:o,pdf:a,svg:r},mimes:e?{[n]:"png",[i]:"jpg",[s]:"webp"}:{[n]:"png",[i]:"jpg",[a]:"pdf",[r]:"svg"}})}toMime(e){return this.formats[(e||"").replace(/^\./,"").toLowerCase()]}fromMime(e){return this.mimes[e]}}function Fs(t,{filename:e="",extension:n="",format:I,page:o,quality:s,matte:a,density:r,outline:l,archive:k}={}){var{fromMime:d,toMime:u,expected:h}=new Ms,k=k||"canvas",x=I||n.replace(/@\d+x$/i,"")||Ls(e),I=d(u(x)||x),L=u(I),g=t.length;if(!x)throw new Error("Cannot determine image format (use a filename extension or 'format' argument)");if(!I)throw new Error(`Unsupported file format "${x}" (expected ${h})`);if(!g)throw new RangeError("Canvas has no associated contexts (try calling getContext or newPage first)");let f,T,y=e.replace(/{(\d*)}/g,(b,m)=>(T=!0,m=parseInt(m,10),f=isFinite(m)?m:isFinite(f)?f:-1,"{}")),_=o>0?o-1:o<0?g+o:void 0;if(isFinite(_)&&_<0||_>=g)throw new RangeError(g==1?`Canvas only has a ‘page 1’ (${_} is out of bounds)`:`Canvas has pages 1–${g} (${_} is out of bounds)`);if(t=isFinite(_)?[t[_]]:T||I=="pdf"?t:t.slice(-1),s===void 0)s=.92;else if(typeof s!="number"||!isFinite(s)||s<0||s>1)throw new TypeError("The quality option must be an number in the 0.0–1.0 range");if(r===void 0){let b=(n||yi(e,x)).match(/@(\d+)x$/i);r=b?parseInt(b[1],10):1}else if(typeof r!="number"||!Number.isInteger(r)||r<1)throw new TypeError("The density option must be a non-negative integer");return l===void 0?l=!0:I=="svg"&&(l=!!l),{filename:e,pattern:y,format:I,mime:L,pages:t,padding:f,quality:s,matte:a,density:r,outline:l,archive:k}}class At{static for(e){return new At().append(e).get()}constructor(){this.crc=-1}get(){return~this.crc}append(e){for(var n=this.crc|0,i=this.table,o=0,s=e.length|0;o<s;o++)n=n>>>8^i[(n^e[o])&255];return this.crc=n,this}}At.prototype.table=(()=>{var t,e,n,i=[];for(t=0;t<256;t++){for(n=t,e=0;e<8;e++)n=n&1?n>>>1^3988292384:n>>>1;i[t]=n}return i})();function Nt(t){let e=new Uint8Array(t),n=new DataView(e.buffer),i={array:e,view:n,size:t,set8(o,s){return n.setUint8(o,s),i},set16(o,s){return n.setUint16(o,s,!0),i},set32(o,s){return n.setUint32(o,s,!0),i},bytes(o,s){return e.set(s,o),i}};return i}class Dt{constructor(e){let n=new Date;Object.assign(this,{directory:e,offset:0,files:[],time:(n.getHours()<<6|n.getMinutes())<<5|n.getSeconds()/2,date:(n.getFullYear()-1980<<4|n.getMonth()+1)<<5|n.getDate()}),this.add(e)}async add(e,n){let i=!n,o=Dt.encoder.encode(`${this.directory}/${i?"":e}`),s=new Uint8Array(i?0:await n.arrayBuffer()),a=30+o.length,r=a+s.length,l=16,{offset:c}=this,d=Nt(26).set32(0,134742036).set16(6,this.time).set16(8,this.date).set32(10,At.for(s)).set32(14,s.length).set32(18,s.length).set16(22,o.length);c+=a;let u=Nt(a+s.length+l).set32(0,67324752).bytes(4,d.array).bytes(30,o).bytes(a,s);c+=s.length,u.set32(r,134695760).bytes(r+4,d.array.slice(10,22)),c+=l,this.files.push({offset:c,folder:i,name:o,header:d,payload:u}),this.offset=c}toBuffer(){let e=this.files.reduce((u,{name:h})=>46+h.length+u,0),n=Nt(e+22),i=0;for(var{offset:o,name:s,header:a,folder:r}of this.files)n.set32(i,33639248).set16(i+4,20).bytes(i+6,a.array).set8(i+38,r?16:0).set32(i+42,o).bytes(i+46,s),i+=46+s.length;n.set32(i,101010256).set16(i+8,this.files.length).set16(i+10,this.files.length).set32(i+12,e).set32(i+16,this.offset);let l=new Uint8Array(this.offset+n.size),c=0;for(var{payload:d}of this.files)l.set(d.array,c),c+=d.size;return l.set(n.array,c),l}get blob(){return new Blob([this.toBuffer()],{type:"application/zip"})}}Dt.encoder=new TextEncoder;const on=(t,e,n,i)=>{if(i){let{width:o,height:s}=t,a=Object.assign(document.createElement("canvas"),{width:o,height:s}),r=a.getContext("2d");r.fillStyle=i,r.fillRect(0,0,o,s),r.drawImage(t,0,0),t=a}return new Promise((o,s)=>t.toBlob(o,e,n))},Ns=(...t)=>on(...t).then(e=>e.arrayBuffer()),zs=async(t,e,n,i,o)=>{bi(o,await on(t,e,n,i))},Rs=async(t,e,n,i,o,s,a)=>{let r=d=>s.replace("{}",String(d+1).padStart(a,"0")),l=yi(o,".zip")||"archive",c=new Dt(l);await Promise.all(t.map(async(d,u)=>{let h=r(u);await c.add(h,await on(d,e,n,i))})),bi(`${l}.zip`,c.blob)},bi=(t,e)=>{const n=window.URL.createObjectURL(e),i=document.createElement("a");i.style.display="none",i.href=n,i.setAttribute("download",t),typeof i.download>"u"&&i.setAttribute("target","_blank"),document.body.appendChild(i),i.click(),document.body.removeChild(i),setTimeout(()=>window.URL.revokeObjectURL(n),100)},Us=(t,e,n)=>t.map(i=>{if(e==1&&!n)return i.canvas;let o=document.createElement("canvas"),s=o.getContext("2d"),a=i.canvas?i.canvas:i;return o.width=a.width*e,o.height=a.height*e,n&&(s.fillStyle=n,s.fillRect(0,0,o.width,o.height)),s.scale(e,e),s.drawImage(a,0,0),o}),$s={asBuffer:Ns,asDownload:zs,asZipDownload:Rs,atScale:Us,options:Fs},{asBuffer:zt,asDownload:js,asZipDownload:Hs,atScale:Rt,options:Ut}=$s,An=Symbol.for("toDataURL");let Gs=class{constructor(e,n){let i=document.createElement("canvas"),o=[];Object.defineProperty(i,"async",{value:!0,writable:!1,enumerable:!0});for(var[s,a]of Object.entries({png:()=>zt(i,"image/png"),jpg:()=>zt(i,"image/jpeg"),pages:()=>o.concat(i).map(r=>r.getContext("2d"))}))Object.defineProperty(i,s,{get:a});return Object.assign(i,{width:e,height:n,newPage(...r){var{width:c,height:d}=i,l=Object.assign(document.createElement("canvas"),{width:c,height:d});l.getContext("2d").drawImage(i,0,0),o.push(l);var[c,d]=r.length?r:[c,d];return Object.assign(i,{width:c,height:d}).getContext("2d")},saveAs(r,l){l=typeof l=="number"?{quality:l}:l;let c=Ut(this.pages,{filename:r,...l}),{pattern:d,padding:u,mime:h,quality:k,matte:x,density:I,archive:L}=c,g=Rt(c.pages,I);return u==null?js(g[0],h,k,x,r):Hs(g,h,k,x,L,d,u)},toBuffer(r="png",l={}){l=typeof l=="number"?{quality:l}:l;let c=Ut(this.pages,{extension:r,...l}),{mime:d,quality:u,matte:h,pages:k,density:x}=c,I=Rt(k,x,h)[0];return zt(I,d,u,h)},[An]:i.toDataURL.bind(i),toDataURL(r="png",l={}){l=typeof l=="number"?{quality:l}:l;let c=Ut(this.pages,{extension:r,...l}),{mime:d,quality:u,matte:h,pages:k,density:x}=c,I=Rt(k,x,h)[0],L=I[I===i?An:"toDataURL"](d,u);return Promise.resolve(L)}})}};const Vs={Canvas:Gs},rt=(t,e,n={},i=n)=>{if(Array.isArray(e))e.forEach(o=>rt(t,o,n,i));else if(typeof e=="function")e(t,n,i,rt);else{const o=Object.keys(e)[0];Array.isArray(e[o])?(i[o]={},rt(t,e[o],n,i[o])):i[o]=e[o](t,n,i,rt)}return n},je=(t,e)=>(n,i,o,s)=>{e(n,i,o)&&s(n,t,i,o)},Ks=(t,e)=>(n,i,o,s)=>{const a=[];let r=n.pos;for(;e(n,i,o);){const l={};if(s(n,t,i,l),n.pos===r)break;r=n.pos,a.push(l)}return a},Ws=t=>({data:t,pos:0}),Ce=()=>t=>t.data[t.pos++],wi=(t=0)=>e=>e.data[e.pos+t],Be=t=>e=>e.data.subarray(e.pos,e.pos+=t),Et=t=>e=>e.data.subarray(e.pos,e.pos+t),Xt=t=>e=>Array.from(Be(t)(e)).map(n=>String.fromCharCode(n)).join(""),ze=t=>e=>{const n=Be(2)(e);return t?(n[1]<<8)+n[0]:(n[0]<<8)+n[1]},Ci=(t,e)=>(n,i,o)=>{const s=typeof e=="function"?e(n,i,o):e,a=Be(t),r=new Array(s);for(var l=0;l<s;l++)r[l]=a(n);return r},Xs=(t,e,n)=>{for(var i=0,o=0;o<n;o++)i+=t[e+o]&&2**(n-o-1);return i},sn=t=>e=>{const n=Ce()(e),i=new Array(8);for(var o=0;o<8;o++)i[7-o]=!!(n&1<<o);return Object.keys(t).reduce((s,a)=>{const r=t[a];return r.length?s[a]=Xs(i,r.index,r.length):s[a]=i[r.index],s},{})};var Ot={blocks:t=>{const n=[],i=t.data.length;for(var o=0,s=Ce()(t);s!==0&&s;s=Ce()(t)){if(t.pos+s>=i){const c=i-t.pos;n.push(Be(c)(t)),o+=c;break}n.push(Be(s)(t)),o+=s}const a=new Uint8Array(o);for(var r=0,l=0;l<n.length;l++)a.set(n[l],r),r+=n[l].length;return a}};const Ys=je({gce:[{codes:Be(2)},{byteSize:Ce()},{extras:sn({future:{index:0,length:3},disposal:{index:3,length:3},userInput:{index:6},transparentColorGiven:{index:7}})},{delay:ze(!0)},{transparentColorIndex:Ce()},{terminator:Ce()}]},t=>{var e=Et(2)(t);return e[0]===33&&e[1]===249}),Zs=je({image:[{code:Ce()},{descriptor:[{left:ze(!0)},{top:ze(!0)},{width:ze(!0)},{height:ze(!0)},{lct:sn({exists:{index:0},interlaced:{index:1},sort:{index:2},future:{index:3,length:2},size:{index:5,length:3}})}]},je({lct:Ci(3,(t,e,n)=>Math.pow(2,n.descriptor.lct.size+1))},(t,e,n)=>n.descriptor.lct.exists),{data:[{minCodeSize:Ce()},Ot]}]},t=>wi()(t)===44),Js=je({text:[{codes:Be(2)},{blockSize:Ce()},{preData:(t,e,n)=>Be(n.text.blockSize)(t)},Ot]},t=>{var e=Et(2)(t);return e[0]===33&&e[1]===1}),qs=je({application:[{codes:Be(2)},{blockSize:Ce()},{id:(t,e,n)=>Xt(n.blockSize)(t)},Ot]},t=>{var e=Et(2)(t);return e[0]===33&&e[1]===255}),Qs=je({comment:[{codes:Be(2)},Ot]},t=>{var e=Et(2)(t);return e[0]===33&&e[1]===254}),ea=[{header:[{signature:Xt(3)},{version:Xt(3)}]},{lsd:[{width:ze(!0)},{height:ze(!0)},{gct:sn({exists:{index:0},resolution:{index:1,length:3},sort:{index:4},size:{index:5,length:3}})},{backgroundColorIndex:Ce()},{pixelAspectRatio:Ce()}]},je({gct:Ci(3,(t,e)=>Math.pow(2,e.lsd.gct.size+1))},(t,e)=>e.lsd.gct.exists),{frames:Ks([Ys,qs,Qs,Zs,Js],t=>{var e=wi()(t);return e===33||e===44})}],ta=(t,e)=>{const n=new Array(t.length),i=t.length/e,o=function(d,u){const h=t.slice(u*e,(u+1)*e);n.splice.apply(n,[d*e,e].concat(h))},s=[0,4,2,1],a=[8,8,4,2];for(var r=0,l=0;l<4;l++)for(var c=s[l];c<i;c+=a[l])o(c,r),r++;return n},na=(t,e,n)=>{const s=n;var a,r,l,c,d,u,h,_,k,x,y,I,b,m,F,S;const L=new Array(n),g=new Array(4096),f=new Array(4096),T=new Array(4097);for(I=t,r=1<<I,d=r+1,a=r+2,h=-1,c=I+1,l=(1<<c)-1,k=0;k<r;k++)g[k]=0,f[k]=k;var y,_,b,m,S,F;for(y=_=b=m=S=F=0,x=0;x<s;){if(m===0){if(_<c){y+=e[F]<<_,_+=8,F++;continue}if(k=y&l,y>>=c,_-=c,k>a||k==d)break;if(k==r){c=I+1,l=(1<<c)-1,a=r+2,h=-1;continue}if(h==-1){T[m++]=f[k],h=k,b=k;continue}for(u=k,k==a&&(T[m++]=b,k=h);k>r;)T[m++]=f[k],k=g[k];b=f[k]&255,T[m++]=b,a<4096&&(g[a]=h,f[a]=b,a++,(a&l)===0&&a<4096&&(c++,l+=a)),h=u}m--,L[S++]=T[m],x++}for(x=S;x<s;x++)L[x]=0;return L},ia=t=>{const e=new Uint8Array(t);return rt(Ws(e),ea)},oa=t=>{const e=t.pixels.length,n=new Uint8ClampedArray(e*4);for(var i=0;i<e;i++){const o=i*4,s=t.pixels[i],a=t.colorTable[s];n[o]=a[0],n[o+1]=a[1],n[o+2]=a[2],n[o+3]=s!==t.transparentIndex?255:0}return n},sa=(t,e,n)=>{if(!t.image){console.warn("gif frame does not have associated image.");return}const{image:i}=t,o=i.descriptor.width*i.descriptor.height;var s=na(i.data.minCodeSize,i.data.blocks,o);i.descriptor.lct.interlaced&&(s=ta(s,i.descriptor.width));const a={pixels:s,dims:{top:t.image.descriptor.top,left:t.image.descriptor.left,width:t.image.descriptor.width,height:t.image.descriptor.height}};return i.descriptor.lct&&i.descriptor.lct.exists?a.colorTable=i.lct:a.colorTable=e,t.gce&&(a.delay=(t.gce.delay||10)*10,a.disposalType=t.gce.extras.disposal,t.gce.extras.transparentColorGiven&&(a.transparentIndex=t.gce.transparentColorIndex)),a.patch=oa(a),a},aa=(t,e)=>t.frames.filter(n=>n.image).map(n=>sa(n,t.gct));function ra(t,e,n){const i=ki(e),o=t-1;let s=0;switch(n){case ue.L:s=Te[o][0];break;case ue.M:s=Te[o][1];break;case ue.Q:s=Te[o][2];break;case ue.H:s=Te[o][3];break}return i<=s}function la(t,e){for(var n=1,i=ki(t),o=0,s=Te.length;o<s;o++){var a=0;switch(e){case ue.L:a=Te[o][0];break;case ue.M:a=Te[o][1];break;case ue.Q:a=Te[o][2];break;case ue.H:a=Te[o][3];break}if(i<=a)break;n++}if(n>Te.length)throw new Error("Too long data");return n}function ki(t){var e=encodeURI(t).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return e.length+(e.length!=Number(t)?3:0)}class ca{constructor(e){this.mode=ge.MODE_8BIT_BYTE,this.parsedData=[],this.data=e;const n=[];for(let i=0,o=this.data.length;i<o;i++){const s=[],a=this.data.charCodeAt(i);a>65536?(s[0]=240|(a&1835008)>>>18,s[1]=128|(a&258048)>>>12,s[2]=128|(a&4032)>>>6,s[3]=128|a&63):a>2048?(s[0]=224|(a&61440)>>>12,s[1]=128|(a&4032)>>>6,s[2]=128|a&63):a>128?(s[0]=192|(a&1984)>>>6,s[1]=128|a&63):s[0]=a,n.push(s)}this.parsedData=Array.prototype.concat.apply([],n),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}getLength(){return this.parsedData.length}write(e){for(let n=0,i=this.parsedData.length;n<i;n++)e.put(this.parsedData[n],8)}}class Le{constructor(e=-1,n=ue.L){this.moduleCount=0,this.dataList=[],this.typeNumber=e,this.errorCorrectLevel=n,this.moduleCount=0,this.dataList=[]}addData(e){if(this.typeNumber<=0)this.typeNumber=la(e,this.errorCorrectLevel);else{if(this.typeNumber>40)throw new Error(`Invalid QR version: ${this.typeNumber}`);if(!ra(this.typeNumber,e,this.errorCorrectLevel))throw new Error(`Data is too long for QR version: ${this.typeNumber}`)}const n=new ca(e);this.dataList.push(n),this.dataCache=void 0}isDark(e,n){if(e<0||this.moduleCount<=e||n<0||this.moduleCount<=n)throw new Error(`${e},${n}`);return this.modules[e][n]}getModuleCount(){return this.moduleCount}make(){this.makeImpl(!1,this.getBestMaskPattern())}makeImpl(e,n){this.moduleCount=this.typeNumber*4+17,this.modules=new Array(this.moduleCount);for(let i=0;i<this.moduleCount;i++){this.modules[i]=new Array(this.moduleCount);for(let o=0;o<this.moduleCount;o++)this.modules[i][o]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,n),this.typeNumber>=7&&this.setupTypeNumber(e),this.dataCache==null&&(this.dataCache=Le.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,n)}setupPositionProbePattern(e,n){for(let i=-1;i<=7;i++)if(!(e+i<=-1||this.moduleCount<=e+i))for(let o=-1;o<=7;o++)n+o<=-1||this.moduleCount<=n+o||(0<=i&&i<=6&&(o==0||o==6)||0<=o&&o<=6&&(i==0||i==6)||2<=i&&i<=4&&2<=o&&o<=4?this.modules[e+i][n+o]=!0:this.modules[e+i][n+o]=!1)}getBestMaskPattern(){if(Number.isInteger(this.maskPattern)&&Object.values(De).includes(this.maskPattern))return this.maskPattern;let e=0,n=0;for(let i=0;i<8;i++){this.makeImpl(!0,i);const o=K.getLostPoint(this);(i==0||e>o)&&(e=o,n=i)}return n}setupTimingPattern(){for(let e=8;e<this.moduleCount-8;e++)this.modules[e][6]==null&&(this.modules[e][6]=e%2==0);for(let e=8;e<this.moduleCount-8;e++)this.modules[6][e]==null&&(this.modules[6][e]=e%2==0)}setupPositionAdjustPattern(){const e=K.getPatternPosition(this.typeNumber);for(let n=0;n<e.length;n++)for(let i=0;i<e.length;i++){const o=e[n],s=e[i];if(this.modules[o][s]==null)for(let a=-2;a<=2;a++)for(let r=-2;r<=2;r++)a==-2||a==2||r==-2||r==2||a==0&&r==0?this.modules[o+a][s+r]=!0:this.modules[o+a][s+r]=!1}}setupTypeNumber(e){const n=K.getBCHTypeNumber(this.typeNumber);for(var i=0;i<18;i++){var o=!e&&(n>>i&1)==1;this.modules[Math.floor(i/3)][i%3+this.moduleCount-8-3]=o}for(var i=0;i<18;i++){var o=!e&&(n>>i&1)==1;this.modules[i%3+this.moduleCount-8-3][Math.floor(i/3)]=o}}setupTypeInfo(e,n){const i=this.errorCorrectLevel<<3|n,o=K.getBCHTypeInfo(i);for(var s=0;s<15;s++){var a=!e&&(o>>s&1)==1;s<6?this.modules[s][8]=a:s<8?this.modules[s+1][8]=a:this.modules[this.moduleCount-15+s][8]=a}for(var s=0;s<15;s++){var a=!e&&(o>>s&1)==1;s<8?this.modules[8][this.moduleCount-s-1]=a:s<9?this.modules[8][15-s-1+1]=a:this.modules[8][15-s-1]=a}this.modules[this.moduleCount-8][8]=!e}mapData(e,n){let i=-1,o=this.moduleCount-1,s=7,a=0;for(let r=this.moduleCount-1;r>0;r-=2)for(r==6&&r--;;){for(let l=0;l<2;l++)if(this.modules[o][r-l]==null){let c=!1;a<e.length&&(c=(e[a]>>>s&1)==1),K.getMask(n,o,r-l)&&(c=!c),this.modules[o][r-l]=c,s--,s==-1&&(a++,s=7)}if(o+=i,o<0||this.moduleCount<=o){o-=i,i=-i;break}}}static createData(e,n,i){const o=Ee.getRSBlocks(e,n),s=new ua;for(var a=0;a<i.length;a++){const l=i[a];s.put(l.mode,4),s.put(l.getLength(),K.getLengthInBits(l.mode,e)),l.write(s)}let r=0;for(var a=0;a<o.length;a++)r+=o[a].dataCount;if(s.getLengthInBits()>r*8)throw new Error(`code length overflow. (${s.getLengthInBits()}>${r*8})`);for(s.getLengthInBits()+4<=r*8&&s.put(0,4);s.getLengthInBits()%8!=0;)s.putBit(!1);for(;!(s.getLengthInBits()>=r*8||(s.put(Le.PAD0,8),s.getLengthInBits()>=r*8));)s.put(Le.PAD1,8);return Le.createBytes(s,o)}static createBytes(e,n){let i=0,o=0,s=0;const a=new Array(n.length),r=new Array(n.length);for(var l=0;l<n.length;l++){const k=n[l].dataCount,x=n[l].totalCount-k;o=Math.max(o,k),s=Math.max(s,x),a[l]=new Array(k);for(var c=0;c<a[l].length;c++)a[l][c]=255&e.buffer[c+i];i+=k;const I=K.getErrorCorrectPolynomial(x),g=new Ze(a[l],I.getLength()-1).mod(I);r[l]=new Array(I.getLength()-1);for(var c=0;c<r[l].length;c++){const T=c+g.getLength()-r[l].length;r[l][c]=T>=0?g.get(T):0}}let d=0;for(var c=0;c<n.length;c++)d+=n[c].totalCount;const u=new Array(d);let h=0;for(var c=0;c<o;c++)for(var l=0;l<n.length;l++)c<a[l].length&&(u[h++]=a[l][c]);for(var c=0;c<s;c++)for(var l=0;l<n.length;l++)c<r[l].length&&(u[h++]=r[l][c]);return u}}Le.PAD0=236;Le.PAD1=17;const ue={L:1,M:0,Q:3,H:2},ge={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},De={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};class K{static getBCHTypeInfo(e){let n=e<<10;for(;K.getBCHDigit(n)-K.getBCHDigit(K.G15)>=0;)n^=K.G15<<K.getBCHDigit(n)-K.getBCHDigit(K.G15);return(e<<10|n)^K.G15_MASK}static getBCHTypeNumber(e){let n=e<<12;for(;K.getBCHDigit(n)-K.getBCHDigit(K.G18)>=0;)n^=K.G18<<K.getBCHDigit(n)-K.getBCHDigit(K.G18);return e<<12|n}static getBCHDigit(e){let n=0;for(;e!=0;)n++,e>>>=1;return n}static getPatternPosition(e){return K.PATTERN_POSITION_TABLE[e-1]}static getMask(e,n,i){switch(e){case De.PATTERN000:return(n+i)%2==0;case De.PATTERN001:return n%2==0;case De.PATTERN010:return i%3==0;case De.PATTERN011:return(n+i)%3==0;case De.PATTERN100:return(Math.floor(n/2)+Math.floor(i/3))%2==0;case De.PATTERN101:return n*i%2+n*i%3==0;case De.PATTERN110:return(n*i%2+n*i%3)%2==0;case De.PATTERN111:return(n*i%3+(n+i)%2)%2==0;default:throw new Error(`bad maskPattern:${e}`)}}static getErrorCorrectPolynomial(e){let n=new Ze([1],0);for(let i=0;i<e;i++)n=n.multiply(new Ze([1,Q.gexp(i)],0));return n}static getLengthInBits(e,n){if(1<=n&&n<10)switch(e){case ge.MODE_NUMBER:return 10;case ge.MODE_ALPHA_NUM:return 9;case ge.MODE_8BIT_BYTE:return 8;case ge.MODE_KANJI:return 8;default:throw new Error(`mode:${e}`)}else if(n<27)switch(e){case ge.MODE_NUMBER:return 12;case ge.MODE_ALPHA_NUM:return 11;case ge.MODE_8BIT_BYTE:return 16;case ge.MODE_KANJI:return 10;default:throw new Error(`mode:${e}`)}else if(n<41)switch(e){case ge.MODE_NUMBER:return 14;case ge.MODE_ALPHA_NUM:return 13;case ge.MODE_8BIT_BYTE:return 16;case ge.MODE_KANJI:return 12;default:throw new Error(`mode:${e}`)}else throw new Error(`type:${n}`)}static getLostPoint(e){const n=e.getModuleCount();let i=0;for(var o=0;o<n;o++)for(var s=0;s<n;s++){let l=0;const c=e.isDark(o,s);for(let d=-1;d<=1;d++)if(!(o+d<0||n<=o+d))for(let u=-1;u<=1;u++)s+u<0||n<=s+u||d==0&&u==0||c==e.isDark(o+d,s+u)&&l++;l>5&&(i+=3+l-5)}for(var o=0;o<n-1;o++)for(var s=0;s<n-1;s++){let d=0;e.isDark(o,s)&&d++,e.isDark(o+1,s)&&d++,e.isDark(o,s+1)&&d++,e.isDark(o+1,s+1)&&d++,(d==0||d==4)&&(i+=3)}for(var o=0;o<n;o++)for(var s=0;s<n-6;s++)e.isDark(o,s)&&!e.isDark(o,s+1)&&e.isDark(o,s+2)&&e.isDark(o,s+3)&&e.isDark(o,s+4)&&!e.isDark(o,s+5)&&e.isDark(o,s+6)&&(i+=40);for(var s=0;s<n;s++)for(var o=0;o<n-6;o++)e.isDark(o,s)&&!e.isDark(o+1,s)&&e.isDark(o+2,s)&&e.isDark(o+3,s)&&e.isDark(o+4,s)&&!e.isDark(o+5,s)&&e.isDark(o+6,s)&&(i+=40);let a=0;for(var s=0;s<n;s++)for(var o=0;o<n;o++)e.isDark(o,s)&&a++;const r=Math.abs(100*a/n/n-50)/5;return i+=r*10,i}}K.PATTERN_POSITION_TABLE=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]];K.G15=1335;K.G18=7973;K.G15_MASK=21522;class Q{static glog(e){if(e<1)throw new Error(`glog(${e})`);return Q.LOG_TABLE[e]}static gexp(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return Q.EXP_TABLE[e]}}Q.EXP_TABLE=new Array(256);Q.LOG_TABLE=new Array(256);Q._constructor=function(){for(var t=0;t<8;t++)Q.EXP_TABLE[t]=1<<t;for(var t=8;t<256;t++)Q.EXP_TABLE[t]=Q.EXP_TABLE[t-4]^Q.EXP_TABLE[t-5]^Q.EXP_TABLE[t-6]^Q.EXP_TABLE[t-8];for(var t=0;t<255;t++)Q.LOG_TABLE[Q.EXP_TABLE[t]]=t}();class Ze{constructor(e,n){if(e.length==null)throw new Error(`${e.length}/${n}`);let i=0;for(;i<e.length&&e[i]==0;)i++;this.num=new Array(e.length-i+n);for(let o=0;o<e.length-i;o++)this.num[o]=e[o+i]}get(e){return this.num[e]}getLength(){return this.num.length}multiply(e){const n=new Array(this.getLength()+e.getLength()-1);for(let i=0;i<this.getLength();i++)for(let o=0;o<e.getLength();o++)n[i+o]^=Q.gexp(Q.glog(this.get(i))+Q.glog(e.get(o)));return new Ze(n,0)}mod(e){if(this.getLength()-e.getLength()<0)return this;const n=Q.glog(this.get(0))-Q.glog(e.get(0)),i=new Array(this.getLength());for(var o=0;o<this.getLength();o++)i[o]=this.get(o);for(var o=0;o<e.getLength();o++)i[o]^=Q.gexp(Q.glog(e.get(o))+n);return new Ze(i,0).mod(e)}}class Ee{constructor(e,n){this.totalCount=e,this.dataCount=n}static getRSBlocks(e,n){const i=Ee.getRsBlockTable(e,n);if(i==null)throw new Error(`bad rs block @ typeNumber:${e}/errorCorrectLevel:${n}`);const o=i.length/3,s=[];for(let a=0;a<o;a++){const r=i[a*3+0],l=i[a*3+1],c=i[a*3+2];for(let d=0;d<r;d++)s.push(new Ee(l,c))}return s}static getRsBlockTable(e,n){switch(n){case ue.L:return Ee.RS_BLOCK_TABLE[(e-1)*4+0];case ue.M:return Ee.RS_BLOCK_TABLE[(e-1)*4+1];case ue.Q:return Ee.RS_BLOCK_TABLE[(e-1)*4+2];case ue.H:return Ee.RS_BLOCK_TABLE[(e-1)*4+3];default:return}}}Ee.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];class ua{constructor(){this.buffer=[],this.length=0}get(e){const n=Math.floor(e/8);return(this.buffer[n]>>>7-e%8&1)==1}put(e,n){for(let i=0;i<n;i++)this.putBit((e>>>n-i-1&1)==1)}getLengthInBits(){return this.length}putBit(e){const n=Math.floor(this.length/8);this.buffer.length<=n&&this.buffer.push(0),e&&(this.buffer[n]|=128>>>this.length%8),this.length++}}const Te=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];var da=100,se=256,Dn=se-1,Oe=4,_i=16,an=1<<_i,Si=10,rn=10,fa=an>>rn,ha=an<<Si-rn,ga=se>>3,Yt=6,ma=1<<Yt,pa=ga*ma,va=30,xi=10,yt=1<<xi,Ti=8,En=1<<Ti,ya=xi+Ti,Ke=1<<ya,On=499,Ln=491,Mn=487,Ii=503,ba=3*Ii;function wa(t,e){var n,i,o,s,a;function r(){n=[],i=new Int32Array(256),o=new Int32Array(se),s=new Int32Array(se),a=new Int32Array(se>>3);var g,f;for(g=0;g<se;g++)f=(g<<Oe+8)/se,n[g]=new Float64Array([f,f,f,0]),s[g]=an/se,o[g]=0}function l(){for(var g=0;g<se;g++)n[g][0]>>=Oe,n[g][1]>>=Oe,n[g][2]>>=Oe,n[g][3]=g}function c(g,f,T,y,_){n[f][0]-=g*(n[f][0]-T)/yt,n[f][1]-=g*(n[f][1]-y)/yt,n[f][2]-=g*(n[f][2]-_)/yt}function d(g,f,T,y,_){for(var b=Math.abs(f-g),m=Math.min(f+g,se),S=f+1,F=f-1,$=1,p,j;S<m||F>b;)j=a[$++],S<m&&(p=n[S++],p[0]-=j*(p[0]-T)/Ke,p[1]-=j*(p[1]-y)/Ke,p[2]-=j*(p[2]-_)/Ke),F>b&&(p=n[F--],p[0]-=j*(p[0]-T)/Ke,p[1]-=j*(p[1]-y)/Ke,p[2]-=j*(p[2]-_)/Ke)}function u(g,f,T){var y=2147483647,_=y,b=-1,m=b,S,F,$,p,j;for(S=0;S<se;S++)F=n[S],$=Math.abs(F[0]-g)+Math.abs(F[1]-f)+Math.abs(F[2]-T),$<y&&(y=$,b=S),p=$-(o[S]>>_i-Oe),p<_&&(_=p,m=S),j=s[S]>>rn,s[S]-=j,o[S]+=j<<Si;return s[b]+=fa,o[b]-=ha,m}function h(){var g,f,T,y,_,b,m=0,S=0;for(g=0;g<se;g++){for(T=n[g],_=g,b=T[1],f=g+1;f<se;f++)y=n[f],y[1]<b&&(_=f,b=y[1]);if(y=n[_],g!=_&&(f=y[0],y[0]=T[0],T[0]=f,f=y[1],y[1]=T[1],T[1]=f,f=y[2],y[2]=T[2],T[2]=f,f=y[3],y[3]=T[3],T[3]=f),b!=m){for(i[m]=S+g>>1,f=m+1;f<b;f++)i[f]=g;m=b,S=g}}for(i[m]=S+Dn>>1,f=m+1;f<256;f++)i[f]=Dn}function k(g,f,T){for(var y,_,b,m=1e3,S=-1,F=i[f],$=F-1;F<se||$>=0;)F<se&&(_=n[F],b=_[1]-f,b>=m?F=se:(F++,b<0&&(b=-b),y=_[0]-g,y<0&&(y=-y),b+=y,b<m&&(y=_[2]-T,y<0&&(y=-y),b+=y,b<m&&(m=b,S=_[3])))),$>=0&&(_=n[$],b=f-_[1],b>=m?$=-1:($--,b<0&&(b=-b),y=_[0]-g,y<0&&(y=-y),b+=y,b<m&&(y=_[2]-T,y<0&&(y=-y),b+=y,b<m&&(m=b,S=_[3]))));return S}function x(){var g,f=t.length,T=30+(e-1)/3,y=f/(3*e),_=~~(y/da),b=yt,m=pa,S=m>>Yt;for(S<=1&&(S=0),g=0;g<S;g++)a[g]=b*((S*S-g*g)*En/(S*S));var F;f<ba?(e=1,F=3):f%On!==0?F=3*On:f%Ln!==0?F=3*Ln:f%Mn!==0?F=3*Mn:F=3*Ii;var $,p,j,P,B=0;for(g=0;g<y;)if($=(t[B]&255)<<Oe,p=(t[B+1]&255)<<Oe,j=(t[B+2]&255)<<Oe,P=u($,p,j),c(b,P,$,p,j),S!==0&&d(S,P,$,p,j),B+=F,B>=f&&(B-=f),g++,_===0&&(_=1),g%_===0)for(b-=b/T,m-=m/va,S=m>>Yt,S<=1&&(S=0),P=0;P<S;P++)a[P]=b*((S*S-P*P)*En/(S*S))}function I(){r(),x(),l(),h()}this.buildColormap=I;function L(){for(var g=[],f=[],T=0;T<se;T++)f[n[T][3]]=T;for(var y=0,_=0;_<se;_++){var b=f[_];g[y++]=n[b][0],g[y++]=n[b][1],g[y++]=n[b][2]}return g}this.getColormap=L,this.lookupRGB=k}var Fn=-1,bt=12,at=5003,Ca=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535];function ka(t,e,n,i){var o=Math.max(2,i),s=new Uint8Array(256),a=new Int32Array(at),r=new Int32Array(at),l,c=0,d,u=0,h,k=!1,x,I,L,g,f,T;function y(P,B){s[d++]=P,d>=254&&F(B)}function _(P){b(at),u=I+2,k=!0,j(I,P)}function b(P){for(var B=0;B<P;++B)a[B]=-1}function m(P,B){var w,ne,G,q,ie,Z,de;for(x=P,k=!1,T=x,h=$(T),I=1<<P-1,L=I+1,u=I+2,d=0,q=p(),de=0,w=at;w<65536;w*=2)++de;de=8-de,Z=at,b(Z),j(I,B);e:for(;(ne=p())!=Fn;){if(w=(ne<<bt)+q,G=ne<<de^q,a[G]===w){q=r[G];continue}else if(a[G]>=0){ie=Z-G,G===0&&(ie=1);do if((G-=ie)<0&&(G+=Z),a[G]===w){q=r[G];continue e}while(a[G]>=0)}j(q,B),q=ne,u<1<<bt?(r[G]=u++,a[G]=w):_(B)}j(q,B),j(L,B)}function S(P){P.writeByte(o),g=t*e,f=0,m(o+1,P),P.writeByte(0)}function F(P){d>0&&(P.writeByte(d),P.writeBytes(s,0,d),d=0)}function $(P){return(1<<P)-1}function p(){if(g===0)return Fn;--g;var P=n[f++];return P&255}function j(P,B){for(l&=Ca[c],c>0?l|=P<<c:l=P,c+=T;c>=8;)y(l&255,B),l>>=8,c-=8;if((u>h||k)&&(k?(h=$(T=x),k=!1):(++T,T==bt?h=1<<bt:h=$(T))),P==L){for(;c>0;)y(l&255,B),l>>=8,c-=8;F(B)}}this.encode=S}function he(){this.page=-1,this.pages=[],this.newPage()}he.pageSize=4096;he.charMap={};for(var wt=0;wt<256;wt++)he.charMap[wt]=String.fromCharCode(wt);he.prototype.newPage=function(){this.pages[++this.page]=new Uint8Array(he.pageSize),this.cursor=0};he.prototype.getData=function(){for(var t="",e=0;e<this.pages.length;e++)for(var n=0;n<he.pageSize;n++)t+=he.charMap[this.pages[e][n]];return t};he.prototype.toFlattenUint8Array=function(){const t=[];for(var e=0;e<this.pages.length;e++)if(e===this.pages.length-1){const i=Uint8Array.from(this.pages[e].slice(0,this.cursor));t.push(i)}else t.push(this.pages[e]);const n=new Uint8Array(t.reduce((i,o)=>i+o.length,0));return t.reduce((i,o)=>(n.set(o,i),i+o.length),0),n};he.prototype.writeByte=function(t){this.cursor>=he.pageSize&&this.newPage(),this.pages[this.page][this.cursor++]=t};he.prototype.writeUTFBytes=function(t){for(var e=t.length,n=0;n<e;n++)this.writeByte(t.charCodeAt(n))};he.prototype.writeBytes=function(t,e,n){for(var i=n||t.length,o=e||0;o<i;o++)this.writeByte(t[o])};function Y(t,e){this.width=~~t,this.height=~~e,this.transparent=null,this.transIndex=0,this.repeat=-1,this.delay=0,this.image=null,this.pixels=null,this.indexedPixels=null,this.colorDepth=null,this.colorTab=null,this.neuQuant=null,this.usedEntry=new Array,this.palSize=7,this.dispose=-1,this.firstFrame=!0,this.sample=10,this.dither=!1,this.globalPalette=!1,this.out=new he}Y.prototype.setDelay=function(t){this.delay=Math.round(t/10)};Y.prototype.setFrameRate=function(t){this.delay=Math.round(100/t)};Y.prototype.setDispose=function(t){t>=0&&(this.dispose=t)};Y.prototype.setRepeat=function(t){this.repeat=t};Y.prototype.setTransparent=function(t){this.transparent=t};Y.prototype.addFrame=function(t){this.image=t,this.colorTab=this.globalPalette&&this.globalPalette.slice?this.globalPalette:null,this.getImagePixels(),this.analyzePixels(),this.globalPalette===!0&&(this.globalPalette=this.colorTab),this.firstFrame&&(this.writeHeader(),this.writeLSD(),this.writePalette(),this.repeat>=0&&this.writeNetscapeExt()),this.writeGraphicCtrlExt(),this.writeImageDesc(),!this.firstFrame&&!this.globalPalette&&this.writePalette(),this.writePixels(),this.firstFrame=!1};Y.prototype.finish=function(){this.out.writeByte(59)};Y.prototype.setQuality=function(t){t<1&&(t=1),this.sample=t};Y.prototype.setDither=function(t){t===!0&&(t="FloydSteinberg"),this.dither=t};Y.prototype.setGlobalPalette=function(t){this.globalPalette=t};Y.prototype.getGlobalPalette=function(){return this.globalPalette&&this.globalPalette.slice&&this.globalPalette.slice(0)||this.globalPalette};Y.prototype.writeHeader=function(){this.out.writeUTFBytes("GIF89a")};Y.prototype.analyzePixels=function(){this.colorTab||(this.neuQuant=new wa(this.pixels,this.sample),this.neuQuant.buildColormap(),this.colorTab=this.neuQuant.getColormap()),this.dither?this.ditherPixels(this.dither.replace("-serpentine",""),this.dither.match(/-serpentine/)!==null):this.indexPixels(),this.pixels=null,this.colorDepth=8,this.palSize=7,this.transparent!==null&&(this.transIndex=this.findClosest(this.transparent,!0))};Y.prototype.indexPixels=function(t){var e=this.pixels.length/3;this.indexedPixels=new Uint8Array(e);for(var n=0,i=0;i<e;i++){var o=this.findClosestRGB(this.pixels[n++]&255,this.pixels[n++]&255,this.pixels[n++]&255);this.usedEntry[o]=!0,this.indexedPixels[i]=o}};Y.prototype.ditherPixels=function(t,e){var n={FalseFloydSteinberg:[[.375,1,0],[.375,0,1],[.25,1,1]],FloydSteinberg:[[.4375,1,0],[.1875,-1,1],[.3125,0,1],[.0625,1,1]],Stucki:[[.19047619047619047,1,0],[.09523809523809523,2,0],[.047619047619047616,-2,1],[.09523809523809523,-1,1],[.19047619047619047,0,1],[.09523809523809523,1,1],[.047619047619047616,2,1],[.023809523809523808,-2,2],[.047619047619047616,-1,2],[.09523809523809523,0,2],[.047619047619047616,1,2],[.023809523809523808,2,2]],Atkinson:[[.125,1,0],[.125,2,0],[.125,-1,1],[.125,0,1],[.125,1,1],[.125,0,2]]};if(!t||!n[t])throw"Unknown dithering kernel: "+t;var i=n[t],o=0,s=this.height,a=this.width,r=this.pixels,l=e?-1:1;this.indexedPixels=new Uint8Array(this.pixels.length/3);for(var c=0;c<s;c++){e&&(l=l*-1);for(var d=l==1?0:a-1,u=l==1?a:0;d!==u;d+=l){o=c*a+d;var h=o*3,k=r[h],x=r[h+1],I=r[h+2];h=this.findClosestRGB(k,x,I),this.usedEntry[h]=!0,this.indexedPixels[o]=h,h*=3;for(var L=this.colorTab[h],g=this.colorTab[h+1],f=this.colorTab[h+2],T=k-L,y=x-g,_=I-f,b=l==1?0:i.length-1,m=l==1?i.length:0;b!==m;b+=l){var S=i[b][1],F=i[b][2];if(S+d>=0&&S+d<a&&F+c>=0&&F+c<s){var $=i[b][0];h=o+S+F*a,h*=3,r[h]=Math.max(0,Math.min(255,r[h]+T*$)),r[h+1]=Math.max(0,Math.min(255,r[h+1]+y*$)),r[h+2]=Math.max(0,Math.min(255,r[h+2]+_*$))}}}}};Y.prototype.findClosest=function(t,e){return this.findClosestRGB((t&16711680)>>16,(t&65280)>>8,t&255,e)};Y.prototype.findClosestRGB=function(t,e,n,i){if(this.colorTab===null)return-1;if(this.neuQuant&&!i)return this.neuQuant.lookupRGB(t,e,n);for(var o=0,s=256*256*256,a=this.colorTab.length,r=0,l=0;r<a;l++){var c=t-(this.colorTab[r++]&255),d=e-(this.colorTab[r++]&255),u=n-(this.colorTab[r++]&255),h=c*c+d*d+u*u;(!i||this.usedEntry[l])&&h<s&&(s=h,o=l)}return o};Y.prototype.getImagePixels=function(){var t=this.width,e=this.height;this.pixels=new Uint8Array(t*e*3);for(var n=this.image,i=0,o=0,s=0;s<e;s++)for(var a=0;a<t;a++)this.pixels[o++]=n[i++],this.pixels[o++]=n[i++],this.pixels[o++]=n[i++],i++};Y.prototype.writeGraphicCtrlExt=function(){this.out.writeByte(33),this.out.writeByte(249),this.out.writeByte(4);var t,e;this.transparent===null?(t=0,e=0):(t=1,e=2),this.dispose>=0&&(e=this.dispose&7),e<<=2,this.out.writeByte(0|e|0|t),this.writeShort(this.delay),this.out.writeByte(this.transIndex),this.out.writeByte(0)};Y.prototype.writeImageDesc=function(){this.out.writeByte(44),this.writeShort(0),this.writeShort(0),this.writeShort(this.width),this.writeShort(this.height),this.firstFrame||this.globalPalette?this.out.writeByte(0):this.out.writeByte(128|this.palSize)};Y.prototype.writeLSD=function(){this.writeShort(this.width),this.writeShort(this.height),this.out.writeByte(240|this.palSize),this.out.writeByte(0),this.out.writeByte(0)};Y.prototype.writeNetscapeExt=function(){this.out.writeByte(33),this.out.writeByte(255),this.out.writeByte(11),this.out.writeUTFBytes("NETSCAPE2.0"),this.out.writeByte(3),this.out.writeByte(1),this.writeShort(this.repeat),this.out.writeByte(0)};Y.prototype.writePalette=function(){this.out.writeBytes(this.colorTab);for(var t=3*256-this.colorTab.length,e=0;e<t;e++)this.out.writeByte(0)};Y.prototype.writeShort=function(t){this.out.writeByte(t&255),this.out.writeByte(t>>8&255)};Y.prototype.writePixels=function(){var t=new ka(this.width,this.height,this.indexedPixels,this.colorDepth);t.encode(this.out)};Y.prototype.stream=function(){return this.out};var _a=function(t,e,n,i){function o(s){return s instanceof n?s:new n(function(a){a(s)})}return new(n||(n=Promise))(function(s,a){function r(d){try{c(i.next(d))}catch(u){a(u)}}function l(d){try{c(i.throw(d))}catch(u){a(u)}}function c(d){d.done?s(d.value):o(d.value).then(r,l)}c((i=i.apply(t,e||[])).next())})};const{Canvas:Ae}=Vs,Ct=.4;function Nn(t){if(!t)return;function e(n){n.onload=null,n.onerror=null}return new Promise(function(n,i){if(t.slice(0,4)=="data"){let s=new Image;s.onload=function(){n(s),e(s)},s.onerror=function(){i("Image load error"),e(s)},s.src=t;return}let o=new Image;o.setAttribute("crossOrigin","Anonymous"),o.onload=function(){n(o)},o.onerror=function(){i("Image load error")},o.src=t})}class V{constructor(e){const n=Object.assign({},e);if(Object.keys(V.defaultOptions).forEach(i=>{i in n||Object.defineProperty(n,i,{value:V.defaultOptions[i],enumerable:!0,writable:!0})}),n.components?typeof n.components=="object"&&Object.keys(V.defaultComponentOptions).forEach(i=>{i in n.components?Object.defineProperty(n.components,i,{value:Object.assign(Object.assign({},V.defaultComponentOptions[i]),n.components[i]),enumerable:!0,writable:!0}):Object.defineProperty(n.components,i,{value:V.defaultComponentOptions[i],enumerable:!0,writable:!0})}):n.components=V.defaultComponentOptions,n.dotScale!==null&&n.dotScale!==void 0){if(n.dotScale<=0||n.dotScale>1)throw new Error("dotScale should be in range (0, 1].");n.components.data.scale=n.dotScale,n.components.timing.scale=n.dotScale,n.components.alignment.scale=n.dotScale}this.options=n,this.canvas=new Ae(e.size,e.size),this.canvasContext=this.canvas.getContext("2d"),this.qrCode=new Le(-1,this.options.correctLevel),Number.isInteger(this.options.maskPattern)&&(this.qrCode.maskPattern=this.options.maskPattern),Number.isInteger(this.options.version)&&(this.qrCode.typeNumber=this.options.version),this.qrCode.addData(this.options.text),this.qrCode.make()}draw(){return new Promise(e=>this._draw().then(e))}_clear(){this.canvasContext.clearRect(0,0,this.canvas.width,this.canvas.height)}static _prepareRoundedCornerClip(e,n,i,o,s,a){e.beginPath(),e.moveTo(n,i),e.arcTo(n+o,i,n+o,i+s,a),e.arcTo(n+o,i+s,n,i+s,a),e.arcTo(n,i+s,n,i,a),e.arcTo(n,i,n+o,i,a),e.closePath()}static _getAverageRGB(e){const i={r:0,g:0,b:0};let o,s,a=-4;const r={r:0,g:0,b:0};let l=0;s=e.naturalHeight||e.height,o=e.naturalWidth||e.width;const d=new Ae(o,s).getContext("2d");if(!d)return i;d.drawImage(e,0,0);let u;try{u=d.getImageData(0,0,o,s)}catch{return i}for(;(a+=5*4)<u.data.length;)u.data[a]>200||u.data[a+1]>200||u.data[a+2]>200||(++l,r.r+=u.data[a],r.g+=u.data[a+1],r.b+=u.data[a+2]);return r.r=~~(r.r/l),r.g=~~(r.g/l),r.b=~~(r.b/l),r}static _drawDot(e,n,i,o,s=0,a=1){e.fillRect((n+s)*o,(i+s)*o,a*o,a*o)}static _drawAlignProtector(e,n,i,o){e.clearRect((n-2)*o,(i-2)*o,5*o,5*o),e.fillRect((n-2)*o,(i-2)*o,5*o,5*o)}static _drawAlign(e,n,i,o,s=0,a=1,r,l){const c=e.fillStyle;e.fillStyle=r,new Array(4).fill(0).map((d,u)=>{V._drawDot(e,n-2+u,i-2,o,s,a),V._drawDot(e,n+2,i-2+u,o,s,a),V._drawDot(e,n+2-u,i+2,o,s,a),V._drawDot(e,n-2,i+2-u,o,s,a)}),V._drawDot(e,n,i,o,s,a),l||(e.fillStyle="rgba(255, 255, 255, 0.6)",new Array(2).fill(0).map((d,u)=>{V._drawDot(e,n-1+u,i-1,o,s,a),V._drawDot(e,n+1,i-1+u,o,s,a),V._drawDot(e,n+1-u,i+1,o,s,a),V._drawDot(e,n-1,i+1-u,o,s,a)})),e.fillStyle=c}_draw(){var e,n,i,o,s,a,r,l,c,d,u,h,k,x,I,L,g,f,T;return _a(this,void 0,void 0,function*(){const y=(e=this.qrCode)===null||e===void 0?void 0:e.moduleCount,_=this.options.size;let b=this.options.margin;(b<0||b*2>=_)&&(b=0);const m=Math.ceil(b),S=_-2*b,F=this.options.whiteMargin,$=this.options.backgroundDimming,p=Math.ceil(S/y),j=p*y,P=j+2*m,B=new Ae(P,P),w=B.getContext("2d");this._clear(),w.save(),w.translate(m,m);const ne=new Ae(P,P),G=ne.getContext("2d");let q=null,ie=[];if(this.options.gifBackground){const O=ia(this.options.gifBackground);if(q=O,ie=aa(O),this.options.autoColor){let N=0,R=0,U=0,ee=0;for(let X=0;X<ie[0].colorTable.length;X++){const J=ie[0].colorTable[X];J[0]>200||J[1]>200||J[2]>200||J[0]===0&&J[1]===0&&J[2]===0||(ee++,N+=J[0],R+=J[1],U+=J[2])}N=~~(N/ee),R=~~(R/ee),U=~~(U/ee),this.options.colorDark=`rgb(${N},${R},${U})`}}else if(this.options.backgroundImage){const O=yield Nn(this.options.backgroundImage);if(this.options.autoColor){const N=V._getAverageRGB(O);this.options.colorDark=`rgb(${N.r},${N.g},${N.b})`}G.drawImage(O,0,0,O.width,O.height,0,0,P,P),G.rect(0,0,P,P),G.fillStyle=$,G.fill()}else G.rect(0,0,P,P),G.fillStyle=this.options.colorLight,G.fill();const Z=K.getPatternPosition(this.qrCode.typeNumber),de=((i=(n=this.options.components)===null||n===void 0?void 0:n.data)===null||i===void 0?void 0:i.scale)||Ct,Qe=(1-de)*.5;for(let O=0;O<y;O++)for(let N=0;N<y;N++){const R=this.qrCode.isDark(O,N),U=N<8&&(O<8||O>=y-8)||N>=y-8&&O<8,ee=O==6&&N>=8&&N<=y-8||N==6&&O>=8&&O<=y-8;let X=U||ee;for(let oe=1;oe<Z.length-1;oe++)X=X||O>=Z[oe]-2&&O<=Z[oe]+2&&N>=Z[oe]-2&&N<=Z[oe]+2;const J=N*p+(X?0:Qe*p),fe=O*p+(X?0:Qe*p);if(w.strokeStyle=R?this.options.colorDark:this.options.colorLight,w.lineWidth=.5,w.fillStyle=R?this.options.colorDark:this.options.colorLight,Z.length===0)X||w.fillRect(J,fe,(X?1:de)*p,(X?1:de)*p);else{const oe=N<y-4&&N>=y-4-5&&O<y-4&&O>=y-4-5;!X&&!oe&&w.fillRect(J,fe,(X?1:de)*p,(X?1:de)*p)}}const le=Z[Z.length-1],dt=this.options.colorLight;if(w.fillStyle=dt,w.fillRect(0,0,8*p,8*p),w.fillRect(0,(y-8)*p,8*p,8*p),w.fillRect((y-8)*p,0,8*p,8*p),!((s=(o=this.options.components)===null||o===void 0?void 0:o.timing)===null||s===void 0)&&s.protectors&&(w.fillRect(8*p,6*p,(y-8-8)*p,p),w.fillRect(6*p,8*p,p,(y-8-8)*p)),!((r=(a=this.options.components)===null||a===void 0?void 0:a.cornerAlignment)===null||r===void 0)&&r.protectors&&V._drawAlignProtector(w,le,le,p),!((c=(l=this.options.components)===null||l===void 0?void 0:l.alignment)===null||c===void 0)&&c.protectors)for(let O=0;O<Z.length;O++)for(let N=0;N<Z.length;N++){const R=Z[N],U=Z[O];if(!(R===6&&(U===6||U===le))){if(U===6&&(R===6||R===le))continue;if(R===le&&U===le)continue;V._drawAlignProtector(w,R,U,p)}}w.fillStyle=this.options.colorDark,w.fillRect(0,0,7*p,p),w.fillRect((y-7)*p,0,7*p,p),w.fillRect(0,6*p,7*p,p),w.fillRect((y-7)*p,6*p,7*p,p),w.fillRect(0,(y-7)*p,7*p,p),w.fillRect(0,(y-7+6)*p,7*p,p),w.fillRect(0,0,p,7*p),w.fillRect(6*p,0,p,7*p),w.fillRect((y-7)*p,0,p,7*p),w.fillRect((y-7+6)*p,0,p,7*p),w.fillRect(0,(y-7)*p,p,7*p),w.fillRect(6*p,(y-7)*p,p,7*p),w.fillRect(2*p,2*p,3*p,3*p),w.fillRect((y-7+2)*p,2*p,3*p,3*p),w.fillRect(2*p,(y-7+2)*p,3*p,3*p);const He=((u=(d=this.options.components)===null||d===void 0?void 0:d.timing)===null||u===void 0?void 0:u.scale)||Ct,et=(1-He)*.5;for(let O=0;O<y-8;O+=2)V._drawDot(w,8+O,6,p,et,He),V._drawDot(w,6,8+O,p,et,He);const tt=((k=(h=this.options.components)===null||h===void 0?void 0:h.cornerAlignment)===null||k===void 0?void 0:k.scale)||Ct,ft=(1-tt)*.5;V._drawAlign(w,le,le,p,ft,tt,this.options.colorDark,((I=(x=this.options.components)===null||x===void 0?void 0:x.cornerAlignment)===null||I===void 0?void 0:I.protectors)||!1);const nt=((g=(L=this.options.components)===null||L===void 0?void 0:L.alignment)===null||g===void 0?void 0:g.scale)||Ct,ht=(1-nt)*.5;for(let O=0;O<Z.length;O++)for(let N=0;N<Z.length;N++){const R=Z[N],U=Z[O];if(!(R===6&&(U===6||U===le))){if(U===6&&(R===6||R===le))continue;if(R===le&&U===le)continue;V._drawAlign(w,R,U,p,ht,nt,this.options.colorDark,((T=(f=this.options.components)===null||f===void 0?void 0:f.alignment)===null||T===void 0?void 0:T.protectors)||!1)}}if(F&&(w.fillStyle=this.options.backgroundColor,w.fillRect(-m,-m,P,m),w.fillRect(-m,j,P,m),w.fillRect(j,-m,m,P),w.fillRect(-m,-m,m,P)),this.options.logoImage){const O=yield Nn(this.options.logoImage);let N=this.options.logoScale,R=this.options.logoMargin,U=this.options.logoCornerRadius;(N<=0||N>=1)&&(N=.2),R<0&&(R=0),U<0&&(U=0);const ee=j*N,X=.5*(P-ee),J=X;w.restore(),w.fillStyle=this.options.logoBackgroundColor,w.save(),V._prepareRoundedCornerClip(w,X-R,J-R,ee+2*R,ee+2*R,U+R),w.clip();const fe=w.globalCompositeOperation;w.globalCompositeOperation="destination-out",w.fill(),w.globalCompositeOperation=fe,w.restore(),w.save(),V._prepareRoundedCornerClip(w,X,J,ee,ee,U),w.clip(),w.drawImage(O,X,J,ee,ee),w.restore(),w.save(),w.translate(m,m)}if(q){let O,N,R,U,ee,X;if(ie.forEach(function(J){O||(O=new Y(_,_),O.setDelay(J.delay),O.setRepeat(0));const{width:fe,height:oe}=J.dims;N||(N=new Ae(fe,oe),R=N.getContext("2d"),R.rect(0,0,N.width,N.height),R.fillStyle="#ffffff",R.fill()),(!U||!X||fe!==U.width||oe!==U.height)&&(U=new Ae(fe,oe),ee=U.getContext("2d"),X=ee.createImageData(fe,oe)),X.data.set(J.patch),ee.putImageData(X,0,0),R.drawImage(U.getContext("2d").canvas,J.dims.left,J.dims.top);const Fe=new Ae(P,P),te=Fe.getContext("2d");te.drawImage(N.getContext("2d").canvas,0,0,P,P),te.rect(0,0,P,P),te.fillStyle=$,te.fill(),te.drawImage(B.getContext("2d").canvas,0,0,P,P);const Pe=new Ae(_,_),it=Pe.getContext("2d");it.drawImage(Fe.getContext("2d").canvas,0,0,_,_),O.addFrame(it.getImageData(0,0,Pe.width,Pe.height).data)}),!O)throw new Error("No frames.");if(O.finish(),zn(this.canvas)){const fe=O.stream().toFlattenUint8Array().reduce((oe,Fe)=>oe+String.fromCharCode(Fe),"");return Promise.resolve(`data:image/gif;base64,${window.btoa(fe)}`)}return Promise.resolve(Buffer.from(O.stream().toFlattenUint8Array()))}else{G.drawImage(B.getContext("2d").canvas,0,0,P,P),w.drawImage(ne.getContext("2d").canvas,-m,-m,P,P);const O=new Ae(_,_);O.getContext("2d").drawImage(B.getContext("2d").canvas,0,0,_,_),this.canvas=O;const R=this.options.gifBackground?"gif":"png";return zn(this.canvas)?Promise.resolve(this.canvas.toDataURL(R)):Promise.resolve(this.canvas.toBuffer(R))}})}}V.CorrectLevel=ue;V.defaultComponentOptions={data:{scale:.4},timing:{scale:.5,protectors:!1},alignment:{scale:.5,protectors:!1},cornerAlignment:{scale:.5,protectors:!0}};V.defaultOptions={text:"",size:400,margin:20,colorDark:"#000000",colorLight:"rgba(255, 255, 255, 0.6)",correctLevel:ue.M,backgroundImage:void 0,backgroundDimming:"rgba(0,0,0,0)",logoImage:void 0,logoScale:.2,logoMargin:4,logoCornerRadius:8,whiteMargin:!0,components:V.defaultComponentOptions,autoColor:!0,logoBackgroundColor:"#ffffff",backgroundColor:"#ffffff"};function zn(t){try{return t instanceof HTMLElement}catch{return typeof t=="object"&&t.nodeType===1&&typeof t.style=="object"&&typeof t.ownerDocument=="object"}}const Sa={props:{text:{type:String,required:!0},qid:{type:String},correctLevel:{type:Number,default:1},size:{type:Number,default:200},margin:{type:Number,default:20},colorDark:{type:String,default:"#000000"},colorLight:{type:String,default:"#FFFFFF"},bgSrc:{type:String,default:void 0},background:{type:String,default:"rgba(0,0,0,0)"},backgroundDimming:{type:String,default:"rgba(0,0,0,0)"},logoSrc:{type:String,default:void 0},logoBackgroundColor:{type:String,default:"rgba(255,255,255,1)"},gifBgSrc:{type:String,default:void 0},logoScale:{type:Number,default:.2},logoMargin:{type:Number,default:0},logoCornerRadius:{type:Number,default:8},whiteMargin:{type:[Boolean,String],default:!0},dotScale:{type:Number,default:1},autoColor:{type:[Boolean,String],default:!0},binarize:{type:[Boolean,String],default:!1},binarizeThreshold:{type:Number,default:128},callback:{type:Function,default:function(){}},bindElement:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},components:{default:function(){return{data:{scale:1},timing:{scale:1,protectors:!1},alignment:{scale:1,protectors:!1},cornerAlignment:{scale:1,protectors:!0}}}}},name:"vue-qr",data(){return{imgUrl:""}},watch:{$props:{deep:!0,handler(){this.main()}}},mounted(){this.main()},methods:{async main(){if(this.gifBgSrc){const n=await Es(this.gifBgSrc),i=this.logoSrc;this.render(void 0,i,n);return}const t=this.bgSrc,e=this.logoSrc;this.render(t,e)},async render(t,e,n){const i=this;new V({gifBackground:n,text:i.text,size:i.size,margin:i.margin,colorDark:i.colorDark,colorLight:i.colorLight,backgroundColor:i.backgroundColor,backgroundImage:t,backgroundDimming:i.backgroundDimming,logoImage:e,logoScale:i.logoScale,logoBackgroundColor:i.logoBackgroundColor,correctLevel:i.correctLevel,logoMargin:i.logoMargin,logoCornerRadius:i.logoCornerRadius,whiteMargin:Ft(i.whiteMargin),dotScale:i.dotScale,autoColor:Ft(i.autoColor),binarize:Ft(i.binarize),binarizeThreshold:i.binarizeThreshold,components:i.components}).draw().then(o=>{this.imgUrl=o,i.callback&&i.callback(o,i.qid)})}}},xa=["src"];function Ta(t,e,n,i,o,s){return n.bindElement?(D(),M("img",{key:0,style:{display:"inline-block"},src:o.imgUrl},null,8,xa)):z("",!0)}const Rn=Jt(Sa,[["render",Ta]]),Ia={id:"app"},Ba={class:"scale"},Pa={class:"micro_header"},Aa={class:"micro_left"},Da={class:"avatar"},Ea=["src"],Oa={class:"t1"},La={class:"micro_main"},Ma={class:"micro_main_top"},Fa={class:"micro_main-sp"},Na={class:"micro_main_temp"},za=["onClick"],Ra={class:"title"},Ua={class:"price"},$a={key:0,class:"micro_main_middle"},ja={class:"micro_main_middle_banner"},Ha={class:"micro_main_middle_content"},Ga={key:1,class:"micro_main_middle"},Va={class:"micro_main_middle_banner"},Ka={class:"micro_main_middle_title"},Wa={class:"micro_main_middle_content"},Xa={key:2,class:"micro_main_bottom onborder"},Ya={key:0,class:"result"},Za={key:1,class:"result"},Ja={key:2,class:"result"},qa={key:3,class:"result"},Qa={key:4,class:"result"},er={key:5,class:"result"},tr={class:"btns"},nr={class:"micro_pay"},ir={key:0,class:"micro_pay_right"},or={class:"noQrCode"},sr={class:"price"},ar={class:"micro_way"},rr={class:"t1"},lr={class:"bd"},cr={class:"t2"},ur={key:3,class:"micro_main_bottom"},dr={class:"micro_pay"},fr={key:0,class:"micro_pay_right"},hr={class:"noQrCode"},gr={class:"price"},mr={class:"micro_way"},pr={class:"t1"},vr={class:"bd"},yr={class:"t2"},br={key:4,class:"btns"},wr={key:5,class:"btns"},Cr={key:6,class:"btns"},kr={__name:"index",props:{userInfo:{type:Object,default:()=>({})},currentItem:{type:Object,default:()=>({})},subStatusDetail:{type:Object,default:()=>({})}},emits:["close"],setup(t,{emit:e}){var P;const{t:n,locale:i}=Oi(),o=H(!1);Li(),location!=null&&location.origin.includes("medon.com.cn")||(location==null||location.origin.includes("medsci.cn"));const s=H(!1),a=t,r=a.userInfo||{},l=(P=a.currentItem)!=null&&P.feeTypes?a.currentItem:a.subStatusDetail,c=H({}),d=H(),u=e,h=H(""),k=H(),x=H(),I=H(),L=H(r!=null&&r.avatar?r==null?void 0:r.avatar:"https://img.medsci.cn/web/img/user_icon.png"),g=H(0),f=H(a.subStatusDetail),T=()=>{L.value="https://img.medsci.cn/web/img/user_icon.png"},y=async()=>{const B=await Vn();g.value=B},_=()=>{window.open("https://www.medsci.cn/about/index.do?id=27")},b=()=>{window.innerWidth>768?s.value=!0:s.value=!1},m=()=>{clearInterval(I.value),u("close")},S=(B,w)=>{c.value=B,d.value=w,(B==null?void 0:B.coinType)=="人民币"&&B.feePrice!=0&&p(B,l.appUuid)};jn(()=>{clearInterval(I.value)});const F=()=>{I.value=Yn(async()=>{(await Xn(x.value)).payStatus==="PAID"&&(location.reload(),clearInterval(I.value))},2e3)},$=()=>{Wt({title:"提示",zIndex:4e3,confirmButtonColor:"#D7813F",message:`取消包月在${f.value.expireAt}号生效，再次使用需要重新订阅。是否确认取消？`}).then(async()=>{await Kn(),Ye.success("取消成功"),m()}).catch(()=>{})},p=async(B,w)=>{if(!c.value.coinType){Ye.warning("请选择订阅服务周期");return}let ne=await Ni(i.value);if(!(r!=null&&r.userId))!ne||ne=="zh"?window.addLoginDom():location.href=location.origin+"/"+i.value+"/login";else{const G={appUuid:w||"",priceId:B.priceId,monthNum:B.monthNum,packageKey:B.packageKey,packageType:B.type};try{o.value=!0;let q=await Wn(G);if(q)if(o.value=!1,B.coinType=="人民币"&&B.feePrice!=0){const ie=q;location.origin.includes(".medsci.cn")||location.origin.includes(".medon.com.cn"),h.value=location.origin+"/payLink/"+encodeURIComponent(ie),x.value=JSON.parse(ie).piId,await F()}else Ye({type:"success",message:n("tool.sS")}),setTimeout(()=>{location.href=q},1e3)}catch{o.value=!1}}},j=async()=>{const B=await Fi("homePayImg");k.value=B.list[0].value};return xt(()=>{window.removeEventListener("resize",b)}),Je(()=>{var B,w,ne,G,q;y(),((w=(B=f.value)==null?void 0:B.feeTypes)==null?void 0:w.length)>0&&i.value=="zh"&&((ne=f.value)==null||ne.feeTypes.forEach((ie,Z)=>{f.value.packageType==ie.type&&(d.value=Z,c.value=ie)})),(l==null?void 0:l.appType)=="写作"&&localStorage.setItem("appWrite-"+l.appUuid,JSON.stringify({appUuid:l.appUuid,directoryMd:l.directoryMd})),b(),window.addEventListener("resize",b),j(),s.value&&(l==null?void 0:l.feeTypes.length)==1&&(l.feeTypes[0].feePrice>0&&((G=l.feeTypes[0])==null?void 0:G.coinType)=="人民币"&&S(l.feeTypes[0],0),l.feeTypes[0].feePrice>0&&((q=l.feeTypes[0])==null?void 0:q.coinType)=="美元"&&S(l.feeTypes[0],0),l.feeTypes[0].feePrice==0&&S(l.feeTypes[0],0))}),(B,w)=>{var ie,Z,de,Qe,le,dt,He,et,tt,ft,nt,ht,O,N,R,U,ee,X,J,fe,oe,Fe;const ne=$t("el-button"),G=Bi,q=Mi("loading");return D(),M("div",Ia,[v("div",Ba,[v("div",Pa,[v("div",Aa,[v("div",Da,[v("img",{src:C(L)?C(L)+"?t="+Date.now():"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=",onError:T,alt:""},null,40,Ea),v("span",Oa,E((ie=C(r))!=null&&ie.realName?(Z=C(r))==null?void 0:Z.realName:(de=C(r))==null?void 0:de.userName),1)])]),v("div",{class:"micro_right"},[v("img",{src:"https://static.medsci.cn/public-image/ms-image/78bf61f0-208d-11ee-ae3d-b3e9904fad92_关闭@2x.png",alt:"",onClick:m})])]),v("div",La,[v("div",Ma,[v("div",Fa,[v("div",Na,[((le=(Qe=C(l))==null?void 0:Qe.feeTypes[0])==null?void 0:le.coinType)=="美元"||((He=(dt=C(l))==null?void 0:dt.feeTypes)==null?void 0:He.length)>1?(D(),M("div",{key:0,class:"swiper-vip",showIndicator:!1,autoPlay:!1,style:dn({transform:`translate(${B.translateVipVal}px)`})},[(D(!0),M(_t,null,jt(C(l).feeTypes,(te,Pe)=>{var it,ln,cn,un;return D(),M("div",{class:ye(["swiper-vip-item",((it=C(f))==null?void 0:it.packageType)=="连续包月"&&te.type=="免费"||((ln=C(f))==null?void 0:ln.packageType)=="连续包月"&&te.type=="连续包年"||((cn=C(f))==null?void 0:cn.packageType)=="连续包年"&&te.type=="免费"||((un=C(f))==null?void 0:un.packageType)=="连续包年"&&te.type=="连续包月"?"noClick":""]),key:Pe,onClick:gl=>S(te,Pe)},[v("div",{class:"newer",style:dn({left:Pe%4==0&&Pe!=0?"6px":"-1px"})},null,4),v("div",{class:ye(["swiper-vip-item-child",{sactvie:C(d)==Pe}])},[v("div",Ra,E(te.type=="免费"?"免费":B.$t(`tool.${te.type}`)),1),v("div",Ua,[v("span",null,E(te.coinType=="人民币"?"¥":"$"),1),W(" "+E(te.feePrice),1)])],2)],10,za)}),128))],4)):z("",!0)])])]),C(i)=="zh"?(D(),M("div",$a,[v("div",ja,[w[7]||(w[7]=v("div",{class:"micro_main_middle_title"},[v("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"}),W("梅斯小智 订阅说明")],-1)),v("div",Ha,[v("div",null,"免费：每个自然月内，每个智能体的使用上限"+E(C(g))+"次。次月开始重新计次。",1),w[5]||(w[5]=v("div",null,"连续包月：订阅之日起一个月内，每个智能体不限使用次数。",-1)),w[6]||(w[6]=v("div",null,"连续包年：订阅之日起一年内，每个智能体不限使用次数",-1))])])])):(D(),M("div",Ga,[v("div",Va,[v("div",Ka,[w[8]||(w[8]=v("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"},null,-1)),W(E((et=C(l))==null?void 0:et.appName),1)]),v("div",Wa,E(C(l).appDescription),1)])])),(tt=C(c))!=null&&tt.coinType&&((ft=C(c))==null?void 0:ft.coinType)=="人民币"&&C(i)=="zh"?(D(),M("div",Xa,[(C(f).subStatus=="1"||C(f).subStatus=="3")&&C(c).type==C(f).packageType?(D(),M("div",Ya,E(C(f).subAt)+" 已订阅",1)):z("",!0),C(f).subStatus=="1"&&C(f).packageType=="免费"&&C(c).type=="免费"?(D(),M("div",Za,"免费使用中…")):z("",!0),C(f).subStatus=="3"?(D(),M("div",Ja,E(C(f).unSubAt)+" 取消订阅",1)):z("",!0),C(f).subStatus=="3"?(D(),M("div",qa,"您的订阅可使用至 "+E(C(f).expireAt),1)):z("",!0),C(f).packageType=="连续包月"&&C(f).subStatus=="1"?(D(),M("div",Qa,"连续包月中…")):z("",!0),C(f).packageType=="连续包年"&&C(f).subStatus=="1"?(D(),M("div",er,"连续包年中…")):z("",!0),v("div",tr,[C(f).packageType=="连续包月"&&C(f).subStatus=="1"?(D(),Ne(ne,{key:0,type:"primary",onClick:w[0]||(w[0]=te=>$())},{default:be(()=>w[9]||(w[9]=[W("取消包月")])),_:1})):z("",!0),C(f).packageType=="连续包年"&&C(f).subStatus=="1"?(D(),Ne(ne,{key:1,type:"primary",onClick:w[1]||(w[1]=te=>$())},{default:be(()=>w[10]||(w[10]=[W("取消包年")])),_:1})):z("",!0)]),v("div",nr,[C(c).type!="免费"&&C(f).subStatus=="0"||C(c).type!="免费"&&C(f).subStatus=="2"||C(f).packageType=="免费"&&C(c).type!="免费"?(D(),M("div",ir,[We(v("div",or,null,512),[[q,C(o)],[Xe,C(o)]]),A(G,null,{default:be(()=>[We(A(Rn,{ref:"qrcode",class:"qr-code",id:"qrcode",correctLevel:3,autoColor:!1,colorDark:"#000000",text:C(h),size:95,margin:0,logoMargin:3},null,8,["text"]),[[Xe,!C(o)]])]),_:1}),v("div",sr,[v("div",ar,[w[11]||(w[11]=v("div",{class:"box"},[v("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""})],-1)),v("span",null,E(B.$t("tool.Support_Alipay_Payment")),1)]),v("span",rr,[W(E(B.$t("tool.Support_Alipay_Payment")),1),v("span",lr,E((nt=C(c))==null?void 0:nt.feePrice),1),W(E(((ht=C(c))==null?void 0:ht.coinType)=="人民币"?"¥":"$")+"/"+E(((O=C(c))==null?void 0:O.monthNum)==3?B.$t("tool.Quarter"):((N=C(c))==null?void 0:N.monthNum)==12?B.$t("tool.Year"):B.$t("tool.Month")),1)]),v("span",cr,E(B.$t("tool.Meisi_Account"))+"："+E(C(r).userName),1),v("span",{class:"t3",onClick:_},[W(E(B.$t("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"))+" ",1),v("img",{onClick:_,src:"https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png",alt:""})])])])):z("",!0)])])):z("",!0),(R=C(c))!=null&&R.coinType&&((U=C(c))==null?void 0:U.coinType)=="人民币"&&C(c).feePrice!=0&&C(i)!="zh"?(D(),M("div",ur,[v("div",dr,[C(f).subStatus=="0"||C(f).subStatus=="2"?(D(),M("div",fr,[We(v("div",hr,null,512),[[q,C(o)],[Xe,C(o)]]),A(G,null,{default:be(()=>[We(A(Rn,{ref:"qrcode",class:"qr-code",id:"qrcode",correctLevel:3,autoColor:!1,colorDark:"#000000",text:C(h),size:95,margin:0,logoMargin:3},null,8,["text"]),[[Xe,!C(o)]])]),_:1}),v("div",gr,[v("div",mr,[w[12]||(w[12]=v("div",{class:"box"},[v("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""})],-1)),v("span",null,E(B.$t("tool.Support_Alipay_Payment")),1)]),v("span",pr,[W(E(B.$t("tool.Support_Alipay_Payment")),1),v("span",vr,E((ee=C(c))==null?void 0:ee.feePrice),1),W(E(((X=C(c))==null?void 0:X.coinType)=="人民币"?"¥":"$")+"/"+E(((J=C(c))==null?void 0:J.monthNum)==3?B.$t("tool.Quarter"):((fe=C(c))==null?void 0:fe.monthNum)==12?B.$t("tool.Year"):B.$t("tool.Month")),1)]),v("span",yr,E(B.$t("tool.Meisi_Account"))+"："+E(C(r).userName),1),v("span",{class:"t3",onClick:_},[W(E(B.$t("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"))+" ",1),v("img",{onClick:_,src:"https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png",alt:""})])])])):z("",!0)])])):z("",!0),C(c).feePrice==0&&(C(f).subStatus=="0"||C(f).subStatus=="2")&&C(i)=="zh"?(D(),M("div",br,[A(ne,{type:"primary",onClick:w[2]||(w[2]=te=>p(C(c),C(l).appUuid))},{default:be(()=>[W(E(B.$t("tool.Free_Trial")),1)]),_:1})])):z("",!0),C(c).feePrice==0&&C(i)!="zh"?(D(),M("div",wr,[A(ne,{type:"primary",onClick:w[3]||(w[3]=te=>p(C(c),C(l).appUuid))},{default:be(()=>[W(E(B.$t("tool.Free_Trial")),1)]),_:1})])):z("",!0),(oe=C(c))!=null&&oe.coinType&&((Fe=C(c))==null?void 0:Fe.coinType)=="美元"&&C(c).feePrice>0?(D(),M("div",Cr,[A(ne,{type:"primary",onClick:w[4]||(w[4]=te=>p(C(c),C(l).appUuid))},{default:be(()=>[W(E(B.$t("market.subscribe")),1)]),_:1})])):z("",!0)])])])}}},Ol=Jt(kr,[["__scopeId","data-v-e7aa7e0c"]]);var _r={};const Sr={name:"Vip",data(){return{isCheckW:!0,isCheckZ:!1,isLogin:!1,activeItem:{},appId:"wx9096048917ec59ab",appOrderId:"",isClick:!1,openId:"",isWx:!1,choseUserVip:{},isFromMedsci:!1,showAll:!1,checkCount:0,vipTypeList:[],activeType:0,active:0,radio:"",isShaking:!1,avatar:"",time:null,piId:"",language:"",num:0}},components:{VanCheckbox:Ss},props:{userInfo:{type:Object,default:()=>({})},currentItem:{type:Object,default:()=>({})},subStatusDetail:{type:Object,default:()=>({})}},created(){},mounted(){var e,n,i,o,s,a,r,l,c,d,u,h,k,x,I,L,g;this.getNum(),((n=(e=this.subStatusDetail)==null?void 0:e.feeTypes)==null?void 0:n.length)>0&&((i=this.subStatusDetail)==null||i.feeTypes.forEach((f,T)=>{this.subStatusDetail.packageType==f.type&&(this.active=T,this.activeItem=f)})),this.language=fn.get("ai_apps_lang");const t=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${t}px`),((o=this.currentItem)==null?void 0:o.appType)=="写作"&&localStorage.setItem("appWrite-"+((s=this.currentItem)==null?void 0:s.appUuid),JSON.stringify({appUuid:(a=this.currentItem)==null?void 0:a.appUuid,directoryMd:(r=this.currentItem)==null?void 0:r.directoryMd})),this.avatar=(l=this.userInfo)!=null&&l.avatar?(c=this.userInfo)==null?void 0:c.avatar:"https://img.medsci.cn/web/img/user_icon.png",this.isUp=(location==null?void 0:location.origin.includes("medon.com.cn"))||(location==null?void 0:location.origin.includes("medsci.cn")),((u=(d=this.currentItem)==null?void 0:d.feeTypes)==null?void 0:u.length)==1&&(((h=this.currentItem)==null?void 0:h.feeTypes[0].feePrice)>0&&this.CheckItem((k=this.currentItem)==null?void 0:k.feeTypes[0],(x=this.currentItem)==null?void 0:x.appUuid),((I=this.currentItem)==null?void 0:I.feeTypes[0].feePrice)==0&&this.CheckItem((L=this.currentItem)==null?void 0:L.feeTypes[0],(g=this.currentItem)==null?void 0:g.appUuid)),hn.get("userInfo")&&JSON.parse(hn.get("userInfo")).userId&&(this.isLogin=!0,this.initUser()),this.init(),(this._.provides[gt]||this.$route).query.source&&(this._.provides[gt]||this.$route).query.source=="medsci"&&(this.isFromMedsci=!0)},methods:{cancelSub(){Wt({title:"提示",zIndex:3002,confirmButtonColor:"#D7813F",message:`取消包月在${this.subStatusDetail.expireAt}号生效，再次使用需要重新订阅。是否确认取消？`}).then(async()=>{await Kn(),Ye.success("取消成功"),this.$emit("close")}).catch(()=>{})},async getNum(){const t=await Vn();this.num=t},changeImg(){this.avatar="https://img.medsci.cn/web/img/user_icon.png"},async subscribe(t,e,n){var o;if(!this.radio&&n){this.isShaking=!0,setTimeout(()=>{this.isShaking=!1},500);return}let i=fn.get("ai_apps_lang");if(!((o=this.userInfo)!=null&&o.userId))!i||i=="zh"?window.addLoginDom():location.href=location.origin+"/"+i+"/login";else{const s={appUuid:e||"",priceId:t.priceId,monthNum:t.monthNum,packageKey:t.packageKey,packageType:t.type};try{let a=await Wn(s);if(a)if(t.coinType=="人民币"&&t.feePrice!=0,t.coinType=="人民币"&&t.feePrice!=0){this.piId=JSON.parse(a).piId,await this.getStatus();let r=await zi(JSON.parse(a));Ye({type:"success",message:this.$t("tool.sS")}),setTimeout(()=>{location.href=r},1e3)}else Ye({type:"success",message:this.$t("tool.sS")}),setTimeout(()=>{location.href=a},1e3)}catch{}}},getStatus(){this.piId,this.time=Yn(async()=>{(await Xn(this.piId)).payStatus==="PAID"&&(location.reload(),clearInterval(this.time))},2e3)},CheckItem(t,e){this.activeItem=t,this.active=e},openActivity(t){t&&(window.location.href=t)},login(){addLoginDom()},initUser(){},init(){},isMedSci(){return navigator.userAgent.includes("medsci_app")},goBack(){window.history.back(-1)},checkFn1(){this.isCheckW=!0,this.isCheckZ=!1},checkFn2(){this.isCheckW=!1,this.isCheckZ=!0},goAgreent(){const t=_r.VITE_MODE=="prod"?"https://www.medsci.cn/about/index.do?id=27":"https://portal-test.medon.com.cn/agreement/27";if(this.isMedSci()){window.location.href=t;return}window.open(t)},createOrder(){this.isWx&&(this.isCheckW=!0,this.isCheckZ=!1);const t={accessAppId:"college",appOrderId:this.appOrderId,payChannel:this.isCheckW?"WX":"ALI",paySource:"MEDSCI_WEB",payType:this.isWx?"JSAPI":"MWEB"};this.$axios.post(api.payBuild,t).then(e=>{e.data,this.orderList(e.data.payOrderId)})},orderList(t){const e={};(this._.provides[gt]||this.$route).query.from&&(e.from="app"),this.isFromMedsci&&(e.sourcefrom="main",e.redirectUrl=(this._.provides[gt]||this.$route).query.redirectUrl);const n={accessAppId:"college",openId:this.isWx?this.openId:"",payOrderId:t,extParam:JSON.stringify(e)};this.$axios.post(api.payOrder,n).then(i=>{if(this.isCheckW){if(this.isWx){this.wxOrder(i.data.wechatJsapi);return}window.location.href=i.data.wechatH5.h5Url}else if(i.data.aliH5.html){const o=document.createElement("div");o.innerHTML=i.data.aliH5.html,document.body.appendChild(o),document.forms[0].submit()}})},wxOrder(t){WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:t.appId,timeStamp:t.timeStamp,nonceStr:t.nonceStr,package:t.packageStr,signType:t.signType,paySign:t.paySign},function(e){e.err_msg=="get_brand_wcpay_request:ok"?(ps.success("支付成功！"),setTimeout(()=>{window.location.reload()},1e3)):e.err_msg=="get_brand_wcpay_request:cancel"})}}},xr={key:0,class:"vip-head"},Tr={class:"vip-introduce"},Ir={class:"box"},Br={key:0,class:"box-left-1"},Pr={class:"left2"},Ar={key:1,class:"box-left"},Dr=["src"],Er={class:"box-word"},Or={class:"t1"},Lr={class:"vip-one"},Mr={class:"big"},Fr={key:0,ref:"scroll"},Nr=["onClick"],zr={class:"title ellipsis-2-lines"},Rr={class:"price"},Ur={key:0,class:"isfava"},$r={key:1,ref:"scroll"},jr=["onClick"],Hr={class:"title ellipsis-2-lines"},Gr={class:"price"},Vr={key:0,class:"isfava"},Kr={key:0},Wr={class:"vip-two",style:{"padding-bottom":"0"}},Xr={class:"vip-two_banner"},Yr={class:"vip-two_content"},Zr={key:0,style:{"text-align":"center"}},Jr={key:1,style:{"text-align":"center"}},qr={key:2,style:{"text-align":"center"}},Qr={key:3,style:{"text-align":"center"}},el={key:4,style:{"text-align":"center"}},tl={key:5,style:{"text-align":"center"}},nl={key:1,class:"vip-two"},il={class:"vip-two_banner"},ol={class:"vip-two_title"},sl={class:"vip-two_content"},al={key:2,class:"vip-three"},rl={key:3,class:"vip-three"},ll={key:0,class:"pay-left"},cl={class:"t1"},ul={key:3,class:"vip-pay btns"},dl={key:0,class:"pay-left"},fl={class:"t1"};function hl(t,e,n,i,o,s){var l,c,d,u,h,k,x,I,L,g,f,T,y,_,b;const a=$t("el-button"),r=$t("van-checkbox");return D(),M("div",{class:ye(["vip",{sp:s.isMedSci()}])},[s.isMedSci()?z("",!0):(D(),M("div",xr,E((l=n.currentItem)==null?void 0:l.appName),1)),v("div",Tr,[e[17]||(e[17]=v("img",{class:"crown",src:"https://static.medsci.cn/public-image/ms-image/1dcd7d10-58af-11ec-8e2f-1389d01aad85_crown.png",alt:""},null,-1)),v("div",Ir,[o.isLogin?(D(),M("div",Ar,[v("img",{class:"avatar",src:o.avatar,alt:"",onError:e[1]||(e[1]=(...m)=>s.changeImg&&s.changeImg(...m))},null,40,Dr),v("div",Er,[v("span",Or,E(n.userInfo.realName||n.userInfo.userName),1)])])):(D(),M("div",Br,[e[16]||(e[16]=v("img",{src:"https://img.medsci.cn/web/img/user_icon.png",alt:""},null,-1)),v("div",Pr,[v("span",{class:"t1",style:{cursor:"pointer"},onClick:e[0]||(e[0]=(...m)=>s.login&&s.login(...m))},"立即登录"),e[15]||(e[15]=v("span",{class:"t2"},"请登录后购买",-1))])]))])]),W(" "+E(n.subStatusDetail.subStatus=="3")+" ",1),v("div",{class:ye(["vip-main",o.language=="zh"&&(n.subStatusDetail.subStatus=="3"||n.subStatusDetail.subStatus=="1"&&n.subStatusDetail.packageType=="免费")&&o.activeItem.type==n.subStatusDetail.packageType?"vip-main-3":""])},[v("div",Lr,[v("div",Mr,[(c=n.currentItem)!=null&&c.feeTypes?(D(),M("ul",Fr,[(D(!0),M(_t,null,jt((d=n.currentItem)==null?void 0:d.feeTypes,(m,S)=>{var F,$,p,j;return D(),M("li",{key:S,class:ye({sactvie:m.type==o.activeItem.type,noClick:((F=n.subStatusDetail)==null?void 0:F.packageType)=="连续包月"&&m.type=="免费"||(($=n.subStatusDetail)==null?void 0:$.packageType)=="连续包月"&&m.type=="连续包年"||((p=n.subStatusDetail)==null?void 0:p.packageType)=="连续包年"&&m.type=="免费"||((j=n.subStatusDetail)==null?void 0:j.packageType)=="连续包年"&&m.type=="连续包月"}),onClick:P=>s.CheckItem(m)},[v("div",zr,E(m.type=="免费"?"免费":t.$t(`tool.${m.type}`)),1),v("div",Rr,[v("span",null,E(m.coinType=="人民币"?"¥":"$"),1),W(E(m.feePrice),1)]),m.originalPrice?(D(),M("div",Ur,E(m.coinType=="人民币"?"¥":"$")+E(m.feePrice),1)):z("",!0)],10,Nr)}),128))],512)):(D(),M("ul",$r,[(D(!0),M(_t,null,jt((u=n.subStatusDetail)==null?void 0:u.feeTypes,(m,S)=>(D(),M("li",{key:S,class:ye({sactvie:m.type==o.activeItem.type}),onClick:F=>s.CheckItem(m)},[v("div",Hr,E(m.type=="免费"?"免费":t.$t(`tool.${m.type}`)),1),v("div",Gr,[v("span",null,E(m.coinType=="人民币"?"¥":"$"),1),W(E(m.feePrice),1)]),m.originalPrice?(D(),M("div",Vr,E(m.coinType=="人民币"?"¥":"$")+E(m.feePrice),1)):z("",!0)],10,jr))),128))],512))])]),o.language=="zh"?(D(),M("div",Kr,[v("div",Wr,[v("div",Xr,[e[20]||(e[20]=v("div",{class:"vip-two_title"},[v("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"}),W("梅斯小智 订阅说明")],-1)),v("div",Yr,[v("div",null,"免费：每个自然月内，每个智能体的使用上限"+E(o.num)+"次。次月开始重新计次。",1),e[18]||(e[18]=v("div",null,"连续包月：订阅之日起一个月内，每个智能体不限使用次数。",-1)),e[19]||(e[19]=v("div",null,"连续包年：订阅之日起一年内，每个智能体不限使用次数",-1))])])]),(n.subStatusDetail.subStatus=="1"||n.subStatusDetail.subStatus=="3")&&o.activeItem.type==n.subStatusDetail.packageType?(D(),M("div",Zr,E(n.subStatusDetail.subAt)+" 已订阅",1)):z("",!0),n.subStatusDetail.subStatus=="1"&&n.subStatusDetail.packageType=="免费"&&o.activeItem.type=="免费"?(D(),M("div",Jr,"免费使用中…")):z("",!0),n.subStatusDetail.subStatus=="3"&&o.activeItem.type==n.subStatusDetail.packageType?(D(),M("div",qr,E(n.subStatusDetail.unSubAt)+" 取消订阅",1)):z("",!0),n.subStatusDetail.subStatus=="3"&&o.activeItem.type==n.subStatusDetail.packageType?(D(),M("div",Qr,"您的订阅可使用至 "+E(n.subStatusDetail.expireAt),1)):z("",!0),n.subStatusDetail.packageType=="连续包月"&&n.subStatusDetail.subStatus=="1"?(D(),M("div",el,"连续包月中…")):z("",!0),n.subStatusDetail.packageType=="连续包年"&&n.subStatusDetail.subStatus=="1"?(D(),M("div",tl,"连续包年中…")):z("",!0)])):(D(),M("div",nl,[v("div",il,[v("div",ol,[e[21]||(e[21]=v("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"},null,-1)),W(E((h=n.currentItem)==null?void 0:h.appName),1)]),v("div",sl,E((k=n.currentItem)==null?void 0:k.appDescription),1)])])),o.activeItem.feePrice>0&&((x=o.activeItem)==null?void 0:x.coinType)=="人民币"&&o.language=="zh"&&o.activeItem.type!="免费"&&n.subStatusDetail.subStatus=="0"||o.activeItem.type!="免费"&&n.subStatusDetail.subStatus=="2"||n.subStatusDetail.packageType=="免费"&&o.activeItem.type!="免费"?(D(),M("div",al,[v("div",{class:ye(["pay",{isWx:o.isWx}])},[e[23]||(e[23]=v("img",{src:"https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png",alt:""},null,-1)),v("div",{class:"item",onClick:e[2]||(e[2]=(...m)=>s.checkFn2&&s.checkFn2(...m))},e[22]||(e[22]=[v("div",{class:"item-left"},[v("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""}),v("span",null,"支付宝支付")],-1),v("div",{class:"item-right isCheck"},[v("img",{src:"https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png",alt:""})],-1)]))],2)])):z("",!0),o.activeItem.feePrice>0&&((I=o.activeItem)==null?void 0:I.coinType)=="人民币"&&o.language!="zh"?(D(),M("div",rl,[v("div",{class:ye(["pay",{isWx:o.isWx}])},[e[25]||(e[25]=v("img",{src:"https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png",alt:""},null,-1)),v("div",{class:"item",onClick:e[3]||(e[3]=(...m)=>s.checkFn2&&s.checkFn2(...m))},e[24]||(e[24]=[v("div",{class:"item-left"},[v("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""}),v("span",null,"支付宝支付")],-1),v("div",{class:"item-right isCheck"},[v("img",{src:"https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png",alt:""})],-1)]))],2)])):z("",!0)],2),o.language=="zh"&&n.subStatusDetail.subStatus=="1"&&n.subStatusDetail.packageType=="连续包月"||o.language=="zh"&&n.subStatusDetail.subStatus=="1"&&o.activeItem.packageType=="连续包年"?(D(),M("div",{key:1,class:ye(["vip-pay btns",n.subStatusDetail.packageType=="连续包月"&&n.subStatusDetail.subStatus=="1"||n.subStatusDetail.packageType=="连续包年"&&n.subStatusDetail.subStatus=="1"?"":"CN_btns"])},[n.subStatusDetail.packageType=="连续包月"&&n.subStatusDetail.subStatus=="1"?(D(),Ne(a,{key:0,onClick:e[4]||(e[4]=m=>s.cancelSub()),type:"primary"},{default:be(()=>e[26]||(e[26]=[W("取消包月")])),_:1})):z("",!0),n.subStatusDetail.packageType=="连续包年"&&n.subStatusDetail.subStatus=="1"?(D(),Ne(a,{key:1,onClick:e[5]||(e[5]=m=>s.cancelSub()),type:"primary"},{default:be(()=>e[27]||(e[27]=[W("取消包年")])),_:1})):z("",!0)],2)):z("",!0),o.activeItem.feePrice>=0&&o.language=="zh"&&(o.activeItem.type=="免费"&&n.subStatusDetail.subStatus=="0"||o.activeItem.type!="免费"&&n.subStatusDetail.subStatus=="0"||o.activeItem.type!="免费"&&n.subStatusDetail.subStatus=="2"||n.subStatusDetail.packageType=="免费"&&o.activeItem.type!="免费")?(D(),M("div",{key:2,class:ye(["vip-pay btns",(n.subStatusDetail.subStatus=="1"||n.subStatusDetail.subStatus=="3")&&o.activeItem.type=="免费"?"CN_btns":""])},[o.activeItem.feePrice!=0&&((L=o.activeItem)==null?void 0:L.coinType)=="人民币"?(D(),M("div",ll,[v("div",cl,E((g=n.currentItem)==null?void 0:g.appName),1),v("div",{class:ye(["t2",{shake:o.isShaking}])},[A(r,{modelValue:o.radio,"onUpdate:modelValue":e[6]||(e[6]=m=>o.radio=m)},null,8,["modelValue"]),v("span",{onClick:e[7]||(e[7]=(...m)=>s.goAgreent&&s.goAgreent(...m))},e[28]||(e[28]=[W("请在阅读并同意"),v("span",null,"协议",-1),W("后开通")]))],2)])):z("",!0),o.activeItem.feePrice!=0&&((f=o.activeItem)==null?void 0:f.coinType)=="人民币"?(D(),M("div",{key:1,class:"pay-right",onClick:e[8]||(e[8]=m=>{var S;return s.subscribe(o.activeItem,(S=n.currentItem)==null?void 0:S.appUuid,"ali")})},[v("span",null,E(o.activeItem.feePrice)+"元确认协议并支付",1)])):z("",!0),o.activeItem.feePrice==0&&(n.subStatusDetail.subStatus=="0"||n.subStatusDetail.subStatus=="2")?(D(),Ne(a,{key:2,onClick:e[9]||(e[9]=m=>{var S;return s.subscribe(o.activeItem,(S=n.currentItem)==null?void 0:S.appUuid)}),type:"primary"},{default:be(()=>[W(E(t.$t("tool.Free_Trial")),1)]),_:1})):z("",!0)],2)):z("",!0),o.activeItem.feePrice>=0&&o.language!="zh"?(D(),M("div",ul,[o.activeItem.feePrice!=0&&((T=o.activeItem)==null?void 0:T.coinType)=="人民币"?(D(),M("div",dl,[v("div",fl,E((y=n.currentItem)==null?void 0:y.appName),1),v("div",{class:ye(["t2",{shake:o.isShaking}])},[A(r,{modelValue:o.radio,"onUpdate:modelValue":e[10]||(e[10]=m=>o.radio=m)},null,8,["modelValue"]),v("span",{onClick:e[11]||(e[11]=(...m)=>s.goAgreent&&s.goAgreent(...m))},e[29]||(e[29]=[W("请在阅读并同意"),v("span",null,"协议",-1),W("后开通")]))],2)])):z("",!0),o.activeItem.feePrice!=0&&((_=o.activeItem)==null?void 0:_.coinType)=="人民币"?(D(),M("div",{key:1,class:"pay-right",onClick:e[12]||(e[12]=m=>{var S;return s.subscribe(o.activeItem,(S=n.currentItem)==null?void 0:S.appUuid,"ali")})},[v("span",null,E(o.activeItem.feePrice)+"元确认协议并支付",1)])):z("",!0),o.activeItem.feePrice==0?(D(),Ne(a,{key:2,onClick:e[13]||(e[13]=m=>{var S;return s.subscribe(o.activeItem,(S=n.currentItem)==null?void 0:S.appUuid)}),type:"primary"},{default:be(()=>[W(E(t.$t("tool.Free_Trial")),1)]),_:1})):z("",!0),o.activeItem.feePrice>0&&((b=o.activeItem)==null?void 0:b.coinType)=="美元"?(D(),Ne(a,{key:3,onClick:e[14]||(e[14]=m=>{var S;return s.subscribe(o.activeItem,(S=n.currentItem)==null?void 0:S.appUuid)}),type:"primary"},{default:be(()=>[W(E(t.$t("market.subscribe")),1)]),_:1})):z("",!0)])):z("",!0)],2)}const Ll=Jt(Sr,[["render",hl],["__scopeId","data-v-51f9193d"]]),Ml=t=>`/img/${encodeURIComponent(t)}`,Fl=(t,e)=>{const n=document.createElement("style");n.type="text/css",n.innerHTML=t,document.head.appendChild(n);var i=document.createElement("script");i.type="text/javascript",i.text=e,document.body.appendChild(i)};export{Ki as A,Bt as B,_l as C,wo as D,Cl as E,tn as F,Bl as G,$i as H,ce as I,Me as J,Pl as K,Sl as L,xl as M,ri as N,To as O,mi as P,Ao as Q,qn as R,di as S,$e as T,en as U,Fl as V,Ll as a,qi as b,wl as c,yn as d,re as e,ve as f,Ml as g,Il as h,qt as i,pe as j,no as k,ei as l,bl as m,ae as n,Qn as o,Ol as p,Tl as q,mn as r,Ds as s,Jn as t,kl as u,oi as v,ii as w,Se as x,me as y,Qo as z};
