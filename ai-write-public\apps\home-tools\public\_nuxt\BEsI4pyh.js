import{_ as a,a as r}from"./TOGsb9HZ.js";import{_ as n}from"./DlAUqK2U.js";import{t as d,v as c,x as e,B as p,O as l}from"./BHVVUzUw.js";const m={name:"AssistantComponent",data(){return{isCollapsed:!1,isQrCodeVisible:!1,isMobile:!1}},mounted(){this.checkMobile(),window.addEventListener("resize",this.checkMobile)},beforeUnmount(){window.removeEventListener("resize",this.checkMobile)},methods:{toggleCollapse(){this.isCollapsed=!this.isCollapsed,this.isCollapsed?setTimeout(()=>{this.isQrCodeVisible=!1},300):this.isQrCodeVisible=!0},checkMobile(){this.isMobile=window.innerWidth<=768,this.isMobile&&(this.isCollapsed=!0,this.isQrCodeVisible=!1)}}};function C(f,s,b,u,i,o){return c(),d("div",{class:l(["assistant-container",{"is-collapsed":i.isCollapsed}])},[e("div",{class:"assistant-icon",onClick:s[0]||(s[0]=(...t)=>o.toggleCollapse&&o.toggleCollapse(...t))},s[1]||(s[1]=[e("img",{src:a,class:"fas fa-user-astronaut",alt:"客服"},null,-1)])),e("div",{class:l(["qr-code",{"is-visible":!i.isCollapsed&&i.isQrCodeVisible}])},s[2]||(s[2]=[e("img",{src:r,alt:"QR Code"},null,-1),p(" 扫码添加小助手 ")]),2)],2)}const g=n(m,[["render",C],["__scopeId","data-v-8961986d"]]);export{g as c};
