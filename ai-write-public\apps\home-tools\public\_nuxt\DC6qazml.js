const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./BeEQ5gKP.js","./BHVVUzUw.js","./entry.BM64DQIl.css","./vs2015.LLWOf3w5.css"])))=>i.map(i=>d[i]);
import{d as ce,r as f,j as se,o as pe,J as le,af as ue,t as I,v as S,O as ye,L as g,x as n,K as ne,ag as Z,ad as ae,y as D,M as fe,N as ge,ah as oe,G as be,u as xe,D as _e,F as $e,z as re,B as we,ai as me}from"./BHVVUzUw.js";import{_ as de}from"./DlAUqK2U.js";import{u as ve,a as Te}from"./DoZnhJ1D.js";import{u as ke,b as Ce}from"./DPV8digk.js";const Le={key:0,class:"render-error"},qe={key:1},Ie=["innerHTML"],Se={key:0,class:"typing-cursor"},Me=ce({__name:"index",props:{content:{},fontSize:{default:"base"},isTyping:{type:Boolean,default:!1}},setup(ee){let T=null,G=null,A=null;const l=ee,m=f(""),b=f(!1),p=new Map,x=f(""),h=f(!1);let L=null;const _={"☺":"☺️",":)":"😊",":-)":"😊",":(":"😢",":-(":"😢",":D":"😃",":-D":"😃",";)":"😉",";-)":"😉",":P":"😛",":-P":"😛",":p":"😛",":-p":"😛",":o":"😮",":-o":"😮",":O":"😱",":-O":"😱",":|":"😐",":-|":"😐",":*":"😘",":-*":"😘","<3":"❤️","</3":"💔","~":"～","。。。":"…","...":"…"},u=new Map,B=new Map;Object.entries(_).forEach(([s,e])=>{if(s.match(/^[:\-\(\)\[\]<>3pPdDoO\|*]+$/))B.set(s,e);else if(s==="~")u.set(s,/~(?=\s|$)/g);else{const i=s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");u.set(s,new RegExp(i,"g"))}});const K=s=>{let e=s;return B.forEach((i,r)=>{e.includes(r)&&(e=e.split(r).join(i))}),u.forEach((i,r)=>{const d=_[r];e=e.replace(i,d)}),e},z=s=>{if(!s)return"";let e=s.trim();return e=e.replace(/(\n\s*){3,}(\|)/g,`

$2`),e=e.replace(/\s{10,}/g," "),e=e.replace(/\n{3,}/g,`

`).replace(/^\s*\n/gm,`
`).replace(/\n\s*$/gm,`
`).replace(/\n\s+\n/g,`

`).replace(/(\|.*\|)\n+/g,`$1
`).replace(/\n{2,}(\|)/g,`

$1`),e},$=se(()=>l.fontSize==="lg"?"text-base":l.fontSize==="base"?"text-sm":"text-xs"),R=()=>{if(!window.mermaid)return;const s=window.mermaid;s.initialized||(s.initialize({startOnLoad:!1,theme:"default",securityLevel:"loose",fontFamily:"inherit"}),s.initialized=!0);const e=l.isTyping?".mermaid-container[data-mermaid]:not(.rendered):not(.rendering)":".mermaid-container[data-mermaid]:not(.rendered)",i=document.querySelectorAll(e);for(const r of i){const d=decodeURIComponent(r.getAttribute("data-mermaid")||""),w=r.id;l.isTyping&&r.classList.add("rendering"),r.classList.add("rendered");try{s.render(w,d).then(({svg:k})=>{r.innerHTML=k,l.isTyping&&r.classList.remove("rendering")}).catch(k=>{console.error("Mermaid render error:",k),r.innerHTML=`
          <div class="mermaid-error">
            <p>Failed to render diagram</p>
          </div>
        `,l.isTyping&&r.classList.remove("rendering")})}catch(k){console.error("Mermaid render error:",k),r.innerHTML=`
        <div class="mermaid-error">
          <p>Failed to render diagram</p>
        </div>
      `,l.isTyping&&r.classList.remove("rendering")}}},E=()=>{if(!window.katex)return;const s=window.katex,e=l.isTyping?".latex-block[data-latex]:not(.rendered):not(.rendering)":".latex-block[data-latex]:not(.rendered)",i=document.querySelectorAll(e);for(const r of i){const d=decodeURIComponent(r.getAttribute("data-latex")||"");l.isTyping&&r.classList.add("rendering"),r.classList.add("rendered");try{const w=s.renderToString(d.trim(),{displayMode:!0});r.innerHTML=w,l.isTyping&&r.classList.remove("rendering")}catch(w){console.error("LaTeX render error:",w),r.innerHTML=`
        <div class="latex-error">
          <p>LaTeX syntax error</p>
        </div>
      `,l.isTyping&&r.classList.remove("rendering")}}},X=async()=>{try{const s=await ae(()=>import("marked"),[],import.meta.url),e=await ae(()=>import("marked-highlight"),[],import.meta.url),i=await ae(()=>import("./BeEQ5gKP.js"),__vite__mapDeps([0,1,2]),import.meta.url);return T=s.marked,G=e.markedHighlight,A=i.default,document.querySelector('link[href*="highlight.js"]')||await ae(()=>Promise.resolve({}),__vite__mapDeps([3]),import.meta.url),!0}catch(s){return console.error("Failed to load plugins:",s),!1}};let j=!1;const U=async()=>{try{if(!T&&!await X())return!1;if(j)return!0;T.use(G({langPrefix:"hljs language-",highlight(e,i){const r=A.getLanguage(i)?i:"plaintext";return A.highlight(e,{language:r}).value}})),T.setOptions({breaks:!0,gfm:!0,headerIds:!1,mangle:!1});const s=new T.Renderer;return s.code=(e,i)=>{if(i==="mermaid"){const w=`mermaid-${Math.random().toString(36).substring(2,11)}`;return`<div class="mermaid-container" data-mermaid="${encodeURIComponent(e.trim())}" id="${w}">
          <div class="mermaid-loading">Rendering diagram...</div>
        </div>`}if(i==="latex"||i==="tex")return`<div class="latex-block" data-latex="${encodeURIComponent(e.trim())}">
          <div class="latex-loading">Rendering LaTeX...</div>
        </div>`;const r=i||"plaintext",d=A.getLanguage(r)?A.highlight(e,{language:r}).value:A.highlightAuto(e).value;return`<pre class="hljs"><code class="hljs language-${r}">${d}</code></pre>`},T.setOptions({renderer:s}),j=!0,!0}catch(s){return console.error("Failed to setup marked plugins:",s),!1}},W=(s,e)=>`final_${btoa(encodeURIComponent(s)).slice(0,32)}`,H=async()=>{const s=l.content;if(l.isTyping)try{b.value=!1;let e=z(s);e=K(e),await U()?m.value=T.parse(e):m.value=J(e),Z(()=>{c()});return}catch(e){console.error("Failed to render typing content:",e),m.value=J(K(s));return}if(!h.value&&s!==x.value)try{h.value=!0,b.value=!1;const e=W(s,!1);if(p.has(e)){m.value=p.get(e),x.value=s,Z(()=>{c()});return}await te(s,e),x.value=s}catch(e){console.error("Failed to render content:",e),b.value=!0,m.value=l.content}finally{h.value=!1}},te=async(s,e)=>{let i=z(s);i=K(i);let r="";if(await U()?r=T.parse(i):r=J(i),!l.isTyping&&(p.set(e,r),p.size>100)){const d=p.keys().next().value;p.delete(d)}m.value=r,Z(()=>{c()})},c=()=>{const s=l.isTyping?10:0;window.requestIdleCallback&&!l.isTyping?window.requestIdleCallback(()=>{R(),E()}):setTimeout(()=>{R(),E()},s)},M=s=>{const e=/^\|(.+)\|\s*\n\|(\s*:?-+:?\s*\|)+\s*\n((\|.+\|\s*\n?)+)/gm;return s.replace(e,i=>{const r=i.trim().split(`
`).map(v=>v.trim());if(r.length<3)return i;const d=r[0],w=r[1],k=r.slice(2).filter(v=>v.trim()),C=d.split("|").map(v=>v.trim()).filter(v=>v),V=w.split("|").map(v=>v.trim()).filter(v=>v).map(v=>v.startsWith(":")&&v.endsWith(":")?"center":v.endsWith(":")?"right":"left"),Y=k.map(v=>v.split("|").map(Q=>Q.trim()).filter(Q=>Q)).filter(v=>v.length>0);let q='<div class="table-container overflow-x-auto my-6 rounded-lg border border-gray-200 shadow-sm">';return q+='<table class="min-w-full divide-y divide-gray-200">',C.length>0&&(q+='<thead class="bg-gray-50">',q+="<tr>",C.forEach((v,Q)=>{const t=V[Q]||"left";q+=`<th class="px-6 py-3 ${t==="center"?"text-center":t==="right"?"text-right":"text-left"} text-xs font-medium text-gray-500 uppercase tracking-wider">${v}</th>`}),q+="</tr>",q+="</thead>"),Y.length>0&&(q+='<tbody class="bg-white divide-y divide-gray-200">',Y.forEach((v,Q)=>{const t=Q%2===0?"bg-white":"bg-gray-50";q+=`<tr class="${t}">`,v.forEach((o,a)=>{const y=V[a]||"left";q+=`<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${y==="center"?"text-center":y==="right"?"text-right":"text-left"}">${o}</td>`}),q+="</tr>"}),q+="</tbody>"),q+="</table></div>",q})},J=s=>{let e=s;e=e.replace(/```(\w+)?\n?([\s\S]*?)```/g,(k,C,V)=>{if(C==="mermaid"){const Y=`mermaid-${Math.random().toString(36).substring(2,11)}`;return`<div class="mermaid-container" data-mermaid="${encodeURIComponent(V.trim())}" id="${Y}">
        <div class="mermaid-loading">Rendering diagram...</div>
      </div>`}return C==="latex"||C==="tex"?`<div class="latex-block" data-latex="${encodeURIComponent(V.trim())}">
        <div class="latex-loading">Rendering LaTeX...</div>
      </div>`:`<div class="code-block-wrapper my-4">
      <pre class="hljs bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
        <code class="hljs language-${C||"plaintext"}">${F(V.trim())}</code>
      </pre>
    </div>`}),e=e.replace(/`([^`\n]+)`/g,'<code class="inline-code bg-gray-100 text-gray-800 px-1.5 py-0.5 rounded text-sm font-mono">$1</code>'),e=e.replace(/^---+\s*$/gm,'<hr class="my-6 border-gray-300">'),e=e.replace(/^\*\*\*+\s*$/gm,'<hr class="my-6 border-gray-300">'),e=e.replace(/^#{6}\s+(.+)$/gm,'<h6 class="text-sm font-medium mt-4 mb-2 text-gray-700">$1</h6>'),e=e.replace(/^#{5}\s+(.+)$/gm,'<h5 class="text-base font-medium mt-4 mb-2 text-gray-700">$1</h5>'),e=e.replace(/^#{4}\s+(.+)$/gm,'<h4 class="text-lg font-medium mt-4 mb-2 text-gray-800">$1</h4>'),e=e.replace(/^#{3}\s+(.+)$/gm,'<h3 class="text-xl font-semibold mt-5 mb-3 text-gray-800">$1</h3>'),e=e.replace(/^#{2}\s+(.+)$/gm,'<h2 class="text-2xl font-semibold mt-6 mb-4 text-gray-900">$1</h2>'),e=e.replace(/^#{1}\s+(.+)$/gm,'<h1 class="text-3xl font-bold mt-8 mb-6 text-gray-900">$1</h1>'),e=e.replace(/^>\s*(.+)$/gm,(k,C)=>`<blockquote class="border-l-4 border-blue-400 pl-4 py-2 my-4 bg-blue-50 text-gray-700 italic">${C}</blockquote>`),e=e.replace(/\*\*\*(.+?)\*\*\*/g,'<strong class="font-bold"><em class="italic">$1</em></strong>'),e=e.replace(/___(.+?)___/g,'<strong class="font-bold"><em class="italic">$1</em></strong>'),e=e.replace(/\*\*(.+?)\*\*/g,'<strong class="font-semibold text-gray-900">$1</strong>'),e=e.replace(/__(.+?)__/g,'<strong class="font-semibold text-gray-900">$1</strong>'),e=e.replace(/\*(.+?)\*/g,'<em class="italic text-gray-700">$1</em>'),e=e.replace(/_(.+?)_/g,'<em class="italic text-gray-700">$1</em>'),e=e.replace(/~~(.+?)~~/g,'<del class="line-through text-gray-500 opacity-75">$1</del>'),e=e.replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2" class="text-blue-600 hover:text-blue-800 underline transition-colors" target="_blank" rel="noopener noreferrer">$1</a>'),e=e.replace(/!\[([^\]]*)\]\(([^)]+)\)/g,'<div class="image-container my-6 text-center"><img src="$2" alt="$1" class="max-w-full h-auto rounded-lg shadow-lg mx-auto"><div class="image-caption mt-2 text-sm text-gray-600 italic">$1</div></div>'),e=M(e),e=e.replace(/^\d+\.\s+(.+)$/gm,'<li class="mb-1">$1</li>'),e=e.replace(/^[\*\-\+]\s+(.+)$/gm,'<li class="mb-1">$1</li>'),e=e.replace(/(<li[^>]*>.*?<\/li>\s*)+/gs,k=>`<ul class="list-disc list-inside my-4 ml-6 space-y-1 text-gray-700">${k}</ul>`),e=e.replace(/(^|[^"])(https?:\/\/[^\s<>"]+)/g,'$1<a href="$2" class="text-blue-600 hover:text-blue-800 underline break-all" target="_blank" rel="noopener noreferrer">$2</a>');const i=e.split(`
`),r=[];let d=!1,w="";for(let k=0;k<i.length;k++){const C=i[k].trim();if(!C){d&&(r.push(`<p class="text-base leading-7 my-4 text-gray-700">${w.trim()}</p>`),w="",d=!1);continue}if(C.match(/^<(h[1-6]|div|blockquote|ul|ol|li|hr|pre|table|img)/)){d&&(r.push(`<p class="text-base leading-7 my-4 text-gray-700">${w.trim()}</p>`),w="",d=!1),r.push(C);continue}d?w+=" "+C:(w=C,d=!0)}return d&&w.trim()&&r.push(`<p class="text-base leading-7 my-4 text-gray-700">${w.trim()}</p>`),r.join(`
`)},F=s=>{const e=document.createElement("div");return e.textContent=s,e.innerHTML},O=()=>{L&&clearTimeout(L);const s=l.isTyping?10:50;L=setTimeout(()=>{H()},s)};return pe(async()=>{l.content&&await H()}),le(()=>l.content,(s,e)=>{s!==e&&(l.isTyping?H():O())},{flush:"post",immediate:!1}),le(()=>l.isTyping,(s,e)=>{e&&!s&&(x.value="",H())}),ue(()=>{L&&clearTimeout(L),p.clear()}),(s,e)=>(S(),I("div",{class:ye(`ai-response-container max-w-4xl ${g($)}`)},[g(b)?(S(),I("div",Le," 渲染内容时发生错误，请检查内容格式。 ")):(S(),I("div",qe,[n("div",{innerHTML:g(m)},null,8,Ie),s.isTyping?(S(),I("span",Se)):ne("",!0)]))],2))}}),ie=de(Me,[["__scopeId","data-v-352912df"]]),Ae={key:0,class:"seo-qa-content","aria-hidden":"true",style:{position:"absolute",left:"-9999px",width:"1px",height:"1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)","white-space":"nowrap"}},Re={class:"qa-conversation"},De={class:"qa-list"},Ee={key:0,class:"question"},je={key:1,class:"answer"},He=["innerHTML"],Ne={class:"seo-metadata"},Be=ce({__name:"SeoQaList",props:{caseId:{},caseTitle:{default:"案例对话"},qaData:{default:()=>[]}},setup(ee){const T=ee,G=l=>l?l.includes("<")&&l.includes(">")?l:l.replace(/\n/g,"<br>"):"",A=se(()=>!T.qaData||T.qaData.length===0?null:{"@context":"https://schema.org","@type":"QAPage",mainEntity:T.qaData.map((l,m)=>({"@type":"Question",name:l.query||`问题 ${m+1}`,text:l.query||`问题 ${m+1}`,answerCount:1,acceptedAnswer:{"@type":"Answer",text:l.answer||"暂无回答",author:{"@type":"Organization",name:"梅斯小智"}}}))});return ve({script:[{type:"application/ld+json",innerHTML:()=>A.value?JSON.stringify(A.value):""}]}),(l,m)=>l.qaData&&l.qaData.length>0?(S(),I("div",Ae,[n("div",Re,[n("h1",null,D(l.caseTitle||"案例对话"),1),n("div",De,[(S(!0),I(fe,null,ge(l.qaData,(b,p)=>(S(),I("div",{key:`qa-${p}`,class:"qa-item"},[b.query?(S(),I("div",Ee,[n("h2",null,D(`问题 ${p+1}`),1),n("p",null,D(b.query),1)])):ne("",!0),b.answer?(S(),I("div",je,[n("h3",null,D(`回答 ${p+1}`),1),n("div",{innerHTML:G(b.answer)},null,8,He)])):ne("",!0)]))),128))]),n("div",Ne,[n("p",null,"案例ID: "+D(l.caseId),1),m[0]||(m[0]=n("p",null,"医学AI智能对话，专业医疗问答，梅斯小智",-1)),m[1]||(m[1]=n("p",null,"关键词: 医学AI, 智能问答, 医疗咨询, 案例分析, 梅斯医学",-1))])])])):ne("",!0)}}),ze=de(Be,[["__scopeId","data-v-901223f0"]]);function Oe(ee={}){const{bottomThreshold:T=50,scrollDuration:G=300,scrollBehavior:A="smooth",userScrollDebounce:l=150,debug:m=!1}=ee,b=f(!0),p=f(!1),x=f(0),h=f(null);let L=null,_=null;const u=(...c)=>{m&&[...c]},B=()=>{if(!h.value)return!1;const c=h.value,M=c.scrollTop,J=c.scrollHeight,F=c.clientHeight,O=J-M-F,s=O<=T;return u("距离底部:",O,"是否接近:",s),s},K=(c=!1)=>{if(!h.value||!b.value&&!c)return;const M=h.value;u("滚动到底部, 强制:",c),M.scrollTo({top:M.scrollHeight,behavior:A})},z=()=>{if(!h.value)return;const c=h.value.scrollTop;Math.abs(c-x.value)>5&&(p.value=!0,c<x.value&&(b.value=!1,u("用户向上滚动，禁用自动滚动")),L&&clearTimeout(L),L=setTimeout(()=>{p.value=!1,B()&&(b.value=!0,u("用户滚动到底部，恢复自动滚动"))},l)),x.value=c},$=()=>{if(h.value){if(p.value){u("用户正在滚动，延迟自动滚动"),_&&clearTimeout(_),_=setTimeout($,100);return}if(!b.value)if(B())b.value=!0,u("检测到接近底部，恢复自动滚动");else return;K()}},R=()=>{u("触摸开始"),p.value=!0},E=()=>{u("触摸结束"),setTimeout(()=>{p.value=!1,B()&&(b.value=!0,u("触摸结束后检测到底部，恢复自动滚动"))},l)},X=c=>{c.deltaY!==0&&(u("鼠标滚轮滚动"),z())},j=c=>{h.value&&U(),h.value=c,x.value=c.scrollTop,c.addEventListener("scroll",z,{passive:!0}),c.addEventListener("wheel",X,{passive:!0}),c.addEventListener("touchstart",R,{passive:!0}),c.addEventListener("touchend",E,{passive:!0}),u("初始化滚动容器")},U=()=>{if(!h.value)return;const c=h.value;c.removeEventListener("scroll",z),c.removeEventListener("wheel",X),c.removeEventListener("touchstart",R),c.removeEventListener("touchend",E),u("移除事件监听器")},W=()=>{b.value=!0,p.value=!1,u("强制启用自动滚动")},H=()=>{b.value=!1,u("禁用自动滚动")},te=()=>{U(),L&&(clearTimeout(L),L=null),_&&(clearTimeout(_),_=null),u("清理资源")};return ue(()=>{te()}),{isAutoScrollEnabled:oe(b),isUserScrolling:oe(p),initScrollContainer:j,smartScroll:$,scrollToBottom:K,enableAutoScroll:W,disableAutoScroll:H,isNearBottom:B,cleanup:te,scrollContainer:oe(h)}}const Pe={class:"relative h-screen",style:{backgroundColor:"var(--bg-main)"}},Ue={class:"h-screen flex flex-col"},Fe={class:"md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3 shadow-sm"},Ve={class:"flex items-center"},Qe={class:"flex items-center flex-1"},Ge={class:"w-8 h-8 bg-gradient-to-r rounded-full flex items-center justify-center mr-3"},Ke=["href"],Xe=["src","alt"],We={class:"text-lg font-semibold text-gray-800"},Je=["title"],Ye={class:"hidden md:block bg-white border-b border-gray-200 px-6 py-4 shadow-sm"},Ze={class:"max-w-4xl mx-auto"},et={class:"flex items-center justify-between"},tt={class:"flex items-center space-x-3"},st={class:"flex flex-col items-center"},at={class:"w-10 h-10 bg-gradient-to-r rounded-full flex items-center justify-center text-white font-medium mb-1"},rt=["href"],nt=["src","alt"],lt={class:"text-xs text-gray-600 font-medium text-center block w-full"},ot={class:"text-lg font-semibold text-gray-800"},it={class:"text-sm"},ct={class:"text-blue-600 font-medium"},ut=["title"],dt={class:"max-w-4xl mx-auto"},mt={key:0,class:"ml-auto max-w-2xl bg-gray-0 border border-gray-200 rounded-2xl rounded-br-md p-4 shadow-sm hover:shadow-md transition-all duration-200"},pt={class:"text-gray-800 text-base leading-relaxed"},ft={key:1,class:"mr-auto max-w-4xl"},gt={class:"text-base text-gray-800 leading-relaxed prose prose-base max-w-none"},vt={key:0},ht={key:1},yt=ce({__name:"[caseId]",props:{currentAppUuid:{default:""}},async setup(ee){let T,G;const A=be(),l=xe(),{t:m,locale:b}=_e(),p=se(()=>A.params.caseId),x=f([]),h=f(!1),L=f(!1),_=f(m("case.title")),u=f(!1),B=f(!1);f(null);const K=f(),z=f(),$=f(""),R=f([]),E=f(!1),X=f(0),j=f([]),U=f({}),W=f({}),{smartScroll:H,initScrollContainer:te,enableAutoScroll:c}=Oe({bottomThreshold:50,scrollBehavior:"smooth",userScrollDebounce:150,debug:!1}),M=f([]),J=f(""),F=f(b.value),{data:O}=([T,G]=$e(async()=>ke(`case-seo-${p.value}`,async()=>{try{const t=Ce();p.value;const o=await me({articleId:"",encryptionId:p.value},t);if(o&&Array.isArray(o)&&o.length>0){const a=o[0];let y=[],N="";if(a&&a.question&&(N=a.question),$.value=a.userName,$.value,a&&a.answer){let P=a.answer;if(typeof P=="string")try{P=JSON.parse(P)}catch(he){return console.error("解析answer数据失败:",he),{qaItems:[],title:""}}Array.isArray(P)&&P.length>0&&(y=P)}return y.length,{qaItems:y,title:N}}return{qaItems:[],title:""}}catch(t){return console.error("服务端预取SEO数据失败:",t),{qaItems:[],title:""}}},{server:!0,default:()=>({qaItems:[],title:""})})),T=await T,G(),T);O.value&&(M.value=O.value.qaItems||[],J.value=O.value.title||"",O.value.title&&(_.value=O.value.title));const s=()=>{l.back()},e=()=>{R.value.forEach(t=>clearTimeout(t)),R.value=[]},i=()=>{const t=F.value?`/${F.value=="zh-CN"?"zh":F.value}`:"",o=$.value||"";return`https://ai.medon.com.cn${t}/${o.replace(" ","-").toLowerCase()}`},r=(t,o)=>{U.value[t]="",W.value[t]=!0,c();let a=0;const y=0,N=()=>{if(a<o.length){U.value[t]+=o[a],a++,a%5===0&&Z(()=>{H()});const P=setTimeout(N,y);R.value.push(P)}else{W.value[t]=!1,Z(()=>{H()});const P=setTimeout(()=>{v(t)},0);R.value.push(P)}};N()},d=()=>{setTimeout(()=>{l.push(F.value?`/${F.value}`:"/")},3e3)},w=async()=>{if(!E.value){E.value=!0;try{const t=await me({articleId:"",encryptionId:p.value});if(t&&Array.isArray(t)&&t.length>0){L.value=!0;const o=t[0];o&&o.question&&(_.value=o.question),k(t)}else d()}catch{d()}finally{E.value=!1}}},k=t=>{if(h.value)return;e(),x.value=[],h.value=!0;let o=[];try{if(t&&Array.isArray(t)&&t.length>0){const a=t[0];if(a&&a.answer){let y=a.answer;if(typeof y=="string")try{y=JSON.parse(y)}catch{d();return}if(Array.isArray(y)&&y.length>0)o=y;else{d();return}}else{d();return}}else{d();return}if(o.length===0){d();return}}catch{d();return}C(o)},C=t=>{h.value=!0;const o=[];t.forEach((a,y)=>{if(a.query&&a.query.trim()){const N={id:`user_${y}_${Date.now()}`,type:"user",content:a.query.trim(),timestamp:new Date};o.push(N)}if(a.answer&&a.answer.trim()){const N={id:`assistant_${y}_${Date.now()}`,type:"assistant",content:a.answer.trim(),timestamp:new Date,isGenerating:!0};o.push(N)}}),V(o)},V=t=>{if(t.length===0){h.value=!1;return}X.value=0,j.value=t,x.value=[],Y()},Y=()=>{const t=X.value,o=j.value;if(t>=o.length){h.value=!1;return}const a=o[t];if(X.value++,x.value=[...x.value,a],Z(()=>{H()}),a.type==="user"){const y=setTimeout(()=>{Y()},300);R.value.push(y)}else a.type==="assistant"&&!u.value&&r(a.id,a.content)},q=()=>{if(u.value)u.value=!1,j.value.length>0&&V(j.value);else if(u.value=!0,B.value=!0,e(),h.value=!1,j.value.length>0){const t=j.value.map(o=>({...o,isGenerating:!1}));x.value=t}},v=t=>{x.value=x.value.map(a=>a.id===t?{...a,isGenerating:!1}:a);const o=setTimeout(()=>{Y()},300);R.value.push(o)};le(()=>p.value,t=>{if(!t){d();return}E.value||L.value&&x.value.length>0||(e(),h.value=!1,x.value=[],L.value=!1,u.value=!1,B.value=!1,w())},{immediate:!0}),le(()=>x.value.length,()=>{Z(()=>{H()})}),pe(()=>{z.value&&te(z.value)}),ue(()=>{e(),E.value=!1}),se(()=>{var t;if(M.value&&M.value.length>0){const o=((t=M.value[0])==null?void 0:t.query)||"",a=m("case.description");return o?`${o} - ${a} - 梅斯小智医学AI智能对话案例`:`${a} - 梅斯小智医学AI智能对话案例`}return`${m("case.description")} - 梅斯小智医学AI智能对话案例`});const Q=se(()=>{const t=`${m("case.caseAnalysis")},${m("case.medicalConsultation")},${m("case.intelligentQA")}`;if(M.value&&M.value.length>0){const a=M.value.map(y=>y.query).filter(Boolean).slice(0,1).join(", ");return a?`${a}, ${t}`:t}return t});return Te({title:()=>`${_.value} - ${$.value} ${m("case.intelligentDialogue")}`,description:()=>`${_.value}`,keywords:()=>Q.value,ogTitle:()=>`${_.value} - ${$.value} ${m("case.intelligentDialogue")}`,ogDescription:()=>`${_.value}`,ogType:"article",ogUrl:()=>`https://ai.medon.com.cn/cases/${p.value}`,twitterCard:"summary",twitterTitle:`${_.value} - ${$.value} ${m("case.intelligentDialogue")}`,twitterDescription:()=>`${_.value}`}),ve({title:()=>`${_.value} - ${$.value} ${m("case.intelligentDialogue")}`}),(t,o)=>(S(),I("div",Pe,[n("div",Ue,[n("div",Fe,[n("div",Ve,[n("button",{onClick:s,class:"flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors mr-3"},o[0]||(o[0]=[n("svg",{class:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[n("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),n("div",Qe,[n("div",Ge,[n("a",{href:i()},[n("img",{src:g($)=="Novax Base"?"https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png":g($).value=="ElavaX Base"?"https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png":"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",alt:g($),class:"w-5 h-5 rounded-full"},null,8,Xe)],8,Ke)]),n("span",We,D(g($)),1)]),n("button",{onClick:q,class:"flex items-center justify-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200",title:g(u)?t.$t("case.viewReplay"):t.$t("case.viewResults")},[n("span",null,D(g(u)?t.$t("case.viewReplay"):t.$t("case.viewResults")),1)],8,Je)])]),n("div",Ye,[n("div",Ze,[n("div",et,[n("div",tt,[n("div",st,[n("div",at,[n("a",{href:i()},[n("img",{src:g($)=="Novax Base"?"https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png":g($).value=="ElavaX Base"?"https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png":"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",alt:g($),class:"w-6 h-6 rounded-full"},null,8,nt)],8,rt)]),n("span",lt,D(g($)),1)]),n("div",null,[n("h1",ot,D(g(_)),1),n("p",it,[we(D(t.$t("case.caseId"))+" ",1),n("span",ct,D(g(p)),1)])])]),n("button",{onClick:q,class:"flex items-center justify-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200 shadow-sm hover:shadow-md",title:g(u)?t.$t("case.viewReplay"):t.$t("case.viewResults")},[n("span",null,D(g(u)?t.$t("case.viewReplay"):t.$t("case.viewResults")),1)],8,ut)])])]),n("div",{ref_key:"messagesContainer",ref:z,class:"flex-1 px-4 py-6 pb-48 overflow-y-auto bg-gray-50 pt-16 md:pt-6"},[n("div",dt,[(S(!0),I(fe,null,ge(g(x),a=>(S(),I("div",{key:a.id,class:"mx-auto max-w-4xl relative group mb-6"},[a.type==="user"?(S(),I("div",mt,[n("div",pt,[re(ie,{content:a.content,"font-size":"base"},null,8,["content"])])])):(S(),I("div",ft,[n("div",gt,[a.isGenerating&&!g(u)&&g(W)[a.id]?(S(),I("div",vt,[re(ie,{content:(g(U)[a.id]||"")+(g(W)[a.id]?"▊":""),"font-size":"lg","is-typing":!0},null,8,["content"])])):(S(),I("div",ht,[re(ie,{content:a.content,"font-size":"lg"},null,8,["content"])]))])]))]))),128)),n("div",{ref_key:"messagesEndRef",ref:K},null,512)])],512)]),re(ze,{"case-id":g(p),"case-title":g(J)||g(_),"qa-data":g(M)},null,8,["case-id","case-title","qa-data"])]))}}),wt=de(yt,[["__scopeId","data-v-896fe663"]]);export{wt as default};
