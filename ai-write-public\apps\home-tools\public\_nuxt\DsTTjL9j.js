import{_ as ne}from"./CjUHxhbH.js";import{C as le,D as X,r as s,j as Z,E as c,F as ee,G as Q,H as O,I as ie,o as te,e as ce,J as re,t as _,v as m,x as e,K as z,L as a,y as l,z as $,A as R,M as ue,N as de,O as me,P as pe,Q as ge,R as _e,Y as ve,aj as he,af as fe,g as q,U as ye,W as Ae,B as W,aX as we,aw as xe,V as ke}from"./BHVVUzUw.js";import{u as oe,b as be}from"./DPV8digk.js";import{s as Ce}from"./x_rD_Ya3.js";import{_ as se}from"./DlAUqK2U.js";import{c as Ie}from"./BEsI4pyh.js";import"./TOGsb9HZ.js";const Se={class:"header ms-header-media"},$e={class:"ms-header"},Te={class:"wrapper"},De={class:"main-menu-placeholder wrapper clearfix",style:{height:"56px",display:"block !important"}},Ue={class:"ms-header-img"},Ne=["href"],Be={id:"main-menu",class:"ms-header-nav"},Le={class:"header-top header-user",id:"user-info-header"},Ee={key:0},Ve={class:"m_font change_lang m_none h-full"},Re={class:"m_font change_lang pc_none"},ze={class:"flex items-center h-full"},He=["href"],Me={key:0,class:"change_lang"},je={class:"current_lang"},Oe={class:"ms-link"},Fe={class:"new-header-avator-pop",id:"new-header-avator"},Ke={class:"new-header-bottom",style:{padding:"0"}},Ye={key:0,class:"langUl"},Qe=["onClick"],Ge={key:1,class:"index-user-img_right"},Pe={href:"#"},Je={class:"img-area"},Ze=["src"],qe={class:"new-header-avator-pop",id:"new-header-avator"},We={class:"new-header-top"},Xe={class:"new-header-info"},et=["src"],tt={class:"new-header-name"},ot={__name:"index",props:{subStatusDetail:{type:Object,default:()=>({})}},emits:["getAppLang","getAppLang","subScript"],async setup(G,{emit:b}){let y,T;const F=b;le();const{setLocale:i,locale:p}=X(),g=s(G.subStatusDetail),r=s(""),u=s(null),U=s(!1),h=s(""),A=s([]),N=s(""),B=s(null),C=s(),I=s();Z(()=>"https://www.medsci.cn/");const d=Z(()=>c.get("userInfo")),n=t=>{r.value="https://img.medsci.cn/web/img/user_icon.png"},f=()=>C.value.style.display="block",w=()=>C.value.style.display="none",K=()=>I.value.style.display="block",Y=()=>I.value.style.display="none",L=()=>{F("subScript")},H=t=>{const o=O("ai_apps_lang",{domain:".medon.com.cn",maxAge:31104e3});o.value=t,h.value=t,i(t),window.sessionStorage.setItem("redirectUrl",t!="zh"?location.origin+"/"+t:location.origin),C.value.style.display="none"},v=async()=>{clearInterval(B.value),c.remove("userInfo",{domain:".medon.com.cn"}),c.remove("userInfo",{domain:".medsci.cn"}),c.remove("userInfo",{domain:"ai.medon.com.cn"}),c.remove("userInfo",{domain:"ai.medsci.cn"}),c.remove("userInfo",{domain:"localhost"}),localStorage.removeItem("conversation"),Object.keys(localStorage).forEach(o=>{o.includes("writeContent")&&localStorage.removeItem(o)});const t=localStorage.getItem("socialType");if(t&&(t==35||t==36))try{await _e(),S(),location.reload()}catch(o){console.error("Logout failed:",o)}else{S();const o=window.location.href,E=window.location.origin.includes("medsci.cn")?`https://www.medsci.cn/sso_logout?redirectUrl=${o}`:`https://portal-test.medon.com.cn/sso_logout?redirectUrl=${o}`;window.location.href=E}},S=()=>{localStorage.removeItem("hasuraToken"),c.remove("yudaoToken",{domain:"ai.medsci.cn"}),c.remove("yudaoToken",{domain:"ai.medon.com.cn"}),c.remove("yudaoToken",{domain:".medsci.cn"}),c.remove("yudaoToken",{domain:".medon.com.cn"}),c.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid")},ae=()=>{var o;const t=ge(p.value);!t||t==="zh"?(o=window.addLoginDom)==null||o.call(window):location.href=location.origin+"/"+p.value+"/login"},P=()=>{var o;const t=c.get("userInfo");t!==JSON.stringify(u.value)&&(u.value=t?JSON.parse(t):null,r.value=((o=u.value)==null?void 0:o.avatar)||"https://img.medsci.cn/web/img/user_icon.png")},{data:yt}=([y,T]=ee(async()=>oe("languages",async()=>{var V;const t=Q(),o=O("userInfo"),E=O("ai_apps_lang"),M=be();u.value=o.value||null,r.value=((V=u.value)==null?void 0:V.avatar)||"https://img.medsci.cn/web/img/user_icon.png";const j=await ie(M);return A.value=j.filter(x=>!x.status).map(x=>({name:x.value,value:x.remark})),h.value=E.value,U.value=t.path.includes("/tool"),null})),y=await y,T(),y);return te(()=>{N.value="https://ai.medon.com.cn/"+p.value,B.value=Ce(P,1e3)}),ce(()=>{clearInterval(B.value)}),re(d,t=>{P()}),(t,o)=>{var M,j,V,x,J;const E=ne;return m(),_("div",Se,[e("div",$e,[e("div",Te,[e("div",De,[e("div",Ue,[e("a",{href:N.value,title:"梅斯小智"},o[1]||(o[1]=[e("img",{src:"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",alt:""},null,-1)]),8,Ne)]),e("div",Be,[e("div",Le,[e("ul",null,[a(p)=="zh"?(m(),_("li",Ee,[e("div",Ve,[e("button",{type:"primary",onClick:L,style:{"background-image":"linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)"},class:"px-[15px] py-[4px] flex items-center h-[28px] mr-[8px] rounded border-none text-xs text-[#614018]"},l(g.value.packageType=="免费"?"升级订阅":g.value.packageType=="连续包月"||g.value.packageType=="连续包年"?"修改订阅":"订阅"),1)]),e("div",Re,[e("button",{type:"primary",onClick:L,style:{"background-image":"linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)"},class:"px-[14px] py-[4px] rounded-[8px] flex items-center mr-[8px] h-[28px] rounded border-none text-xs text-[#614018] whitespace-nowrap"},l(g.value.packageType=="免费"?"升级订阅":g.value.packageType=="连续包月"||g.value.packageType=="连续包年"?"修改订阅":"订阅"),1)])])):z("",!0),e("li",null,[e("div",ze,[e("a",{class:"backImg",target:"_top",title:"梅斯小智",href:N.value},l(t.$t("tool.backtohome")),9,He)])]),e("li",{class:"index-user-img index-user-img_left",onMouseover:f,onMouseout:w,onClick:f},[U.value?z("",!0):(m(),_("div",Me,[e("span",je,l((M=A.value.filter(k=>k.value==h.value)[0])==null?void 0:M.name),1),e("span",Oe,l(t.$t("market.switch")),1)])),e("div",{class:"ms-dropdown-menu",ref_key:"menu",ref:C},[e("div",Fe,[e("div",Ke,[$(E,null,{default:R(()=>[A.value.length>0?(m(),_("div",Ye,[(m(!0),_(ue,null,de(A.value,k=>(m(),_("p",{key:k,onClick:pe(At=>H(k.value),["stop"]),class:me({langItemSelected:k.value===h.value})},l(k.name),11,Qe))),128))])):z("",!0)]),_:1})])])],512)],32),(j=u.value)!=null&&j.userId?(m(),_("li",{key:2,class:"index-user-img",onMouseover:K,onMouseout:Y},[e("a",Pe,[e("div",Je,[e("img",{src:r.value?r.value+"?t="+Date.now():"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=",onError:n,alt:""},null,40,Ze)])]),e("div",{class:"ms-dropdown-menu",ref_key:"menu1",ref:I},[e("div",qe,[e("a",{class:"new-header-exit ms-statis","ms-statis":"logout",href:"#",onClick:o[0]||(o[0]=k=>v())},l(t.$t("market.logout")),1),e("div",We,[e("div",Xe,[e("img",{class:"new-header-avatar",src:r.value?r.value+"?t="+Date.now():"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=",onError:n,alt:""},null,40,et),e("div",tt,[e("span",null,l((V=u.value)!=null&&V.realName?(x=u.value)==null?void 0:x.realName:(J=u.value)==null?void 0:J.userName),1)])])])])],512)],32)):(m(),_("li",Ge,[e("a",{href:"javascript: void(0)",class:"ms-link",onClick:ae},l(t.$t("market.login")),1)]))])])])])])])])}}},st=se(ot,[["__scopeId","data-v-22777163"]]),at={class:"flex-1 flex flex-col"},nt={key:0,class:"text-gray-500 mb-2"},lt=["href"],it=["textContent"],ct=["textContent"],rt={class:"flex items-center my-2"},ut=["src"],dt={class:"text-lg font-bold text-[#666666] text-dark-200 mr-4"},mt={class:"flex-1"},pt={class:"flex items-center my-2"},gt=["src"],_t={class:"text-xl font-bold text-dark-200"},vt={class:"text-dark-500 mb-6"},ht={class:"flex justify-center"},ft={__name:"tools",async setup(G){var I;let b,y;const{locale:T}=X(),F=Q();s([]),s(null);const i=s(null),p=s(!1),D=s(0),g=s(null),r=s(!1),u=navigator.browserLanguage||navigator.language;(I=F.params)!=null&&I.lang;const U=s(""),h=s(null),{data:A}=([b,y]=ee(async()=>oe("getAppList",async()=>{const d=Q(),n=null;let f;T.value=="zh"&&(f=await ve(n));const w=await he(d.params.appUuid,n);return w?[w,f]:null},{server:!0})),b=await b,y(),b);i.value=A.value[0],g.value=A.value[1];const N=()=>{p.value=!0},B=()=>{h.value.pageRef.subScript()},C=d=>{r.value=d};return te(async()=>{setTimeout(()=>{h.value},1e3);const d=O("ai_apps_lang");U.value="https://ai.medon.com.cn/"+T.value,d.value=="zh"&&(r.value=!0),D.value=document.body.clientHeight-56,window.addEventListener("resize",()=>{D.value=document.body.clientHeight-56})}),fe(()=>{window.removeEventListener("resize",()=>{D.value=document.body.clientHeight-56})}),(d,n)=>{var L,H;const f=st,w=q("el-button"),K=we,Y=q("el-dialog");return m(),_("div",null,[$(f,{onIsZHChange:C,subStatusDetail:a(g),onSubScript:B},null,8,["subStatusDetail"]),a(r)?(m(),ye(Ie,{key:0})):z("",!0),e("div",{class:"h-full flex p-6",style:ke({height:`${a(D)}px`})},[e("main",at,[e("div",null,[a(i)?(m(),_("div",nt,[e("a",{class:"cursor-pointer hover:text-[#5298FF]",href:a(U),title:"梅斯小智"},l(d.$t("tool.home")),9,lt),n[2]||(n[2]=e("span",{class:"line",textContent:"/"},null,-1)),e("span",{textContent:l(a(i).appType?d.$t(`${a(Ae)[a(i).appType]}`):"")},null,8,it),n[3]||(n[3]=e("span",{class:"line",textContent:"/"},null,-1)),e("span",{textContent:l(a(i).appName)},null,8,ct)])):z("",!0),e("div",rt,[e("img",{class:"w-[38px] h-[38px] mr-4",src:((L=a(i))==null?void 0:L.appIcon)||"https://img.medsci.cn/web/prod/img/user_icon.png",alt:"这是icon"},null,8,ut),e("h3",dt,l((H=a(i))==null?void 0:H.appName),1),$(w,{plain:"",size:"small",onClick:N},{default:R(()=>[W(l(d.$t("tool.intro")),1)]),_:1})])]),e("div",mt,[$(K,{"current-item":a(i),subStatusDetail:a(g),ref_key:"pageRef",ref:h},null,8,["current-item","subStatusDetail"])])]),$(Y,{modelValue:a(p),"onUpdate:modelValue":n[1]||(n[1]=v=>xe(p)?p.value=v:null),"show-close":!1,width:"500"},{header:R(()=>{var v,S;return[e("div",pt,[e("img",{class:"w-[38px] h-[38px] mr-4",src:((v=a(i))==null?void 0:v.appIcon)||"https://img.medsci.cn/web/prod/img/user_icon.png",alt:"这是icon"},null,8,gt),e("div",_t,l((S=a(i))==null?void 0:S.appName),1)])]}),default:R(()=>{var v;return[e("div",vt,l((v=a(i))==null?void 0:v.appDescription),1),e("div",ht,[$(w,{onClick:n[0]||(n[0]=S=>p.value=!1)},{default:R(()=>n[4]||(n[4]=[W("我知道了")])),_:1})])]}),_:1},8,["modelValue"])],4)])}}},$t=se(ft,[["__scopeId","data-v-4f0716be"]]);export{$t as default};
